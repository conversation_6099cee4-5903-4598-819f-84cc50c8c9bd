using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;

namespace ColdVor.RTS
{
    /// <summary>
    /// Manages pathfinding for units using Unity's NavMesh system
    /// Handles formation movement and group pathfinding
    /// </summary>
    public class PathfindingManager : MonoBehaviour
    {
        [Header("Formation Settings")]
        [SerializeField] private float formationSpacing = 2f;
        [SerializeField] private float formationDepth = 2f;
        [SerializeField] private FormationType defaultFormation = FormationType.Box;

        [Header("Pathfinding Settings")]
        [SerializeField] private float pathUpdateInterval = 0.1f;
        [SerializeField] private float arrivalThreshold = 0.5f;
        [SerializeField] private int maxUnitsPerFormation = 50;



        private static PathfindingManager instance;
        public static PathfindingManager Instance => instance;

        private Dictionary<int, FormationGroup> activeFormations = new Dictionary<int, FormationGroup>();
        private int nextFormationId = 0;

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Move a single unit to a destination
        /// </summary>
        public bool MoveUnit(Unit unit, Vector3 destination)
        {
            if (unit == null || !unit.CanMove) return false;

            // Remove unit from any existing formation
            RemoveUnitFromFormations(unit);

            // Set direct destination
            return unit.MoveTo(destination);
        }

        /// <summary>
        /// Move multiple units in formation to a destination
        /// </summary>
        public bool MoveUnitsInFormation(List<Unit> units, Vector3 destination, FormationType formation = FormationType.Auto)
        {
            if (units == null || units.Count == 0) return false;

            // Filter out units that can't move
            List<Unit> movableUnits = new List<Unit>();
            foreach (var unit in units)
            {
                if (unit != null && unit.CanMove)
                {
                    movableUnits.Add(unit);
                }
            }

            if (movableUnits.Count == 0) return false;

            // Single unit - use direct movement
            if (movableUnits.Count == 1)
            {
                return MoveUnit(movableUnits[0], destination);
            }

            // Multiple units - use formation
            FormationType selectedFormation = formation == FormationType.Auto ?
                GetBestFormationForUnits(movableUnits) : formation;

            return CreateFormationMovement(movableUnits, destination, selectedFormation);
        }

        private bool CreateFormationMovement(List<Unit> units, Vector3 destination, FormationType formation)
        {
            // Remove units from existing formations
            foreach (var unit in units)
            {
                RemoveUnitFromFormations(unit);
            }

            // Create new formation
            int formationId = nextFormationId++;
            FormationGroup formationGroup = new FormationGroup
            {
                id = formationId,
                units = new List<Unit>(units),
                destination = destination,
                formation = formation,
                spacing = formationSpacing,
                depth = formationDepth
            };

            // Calculate formation positions
            List<Vector3> formationPositions = CalculateFormationPositions(units.Count, destination, formation);

            // Assign units to formation positions
            AssignUnitsToFormationPositions(formationGroup, formationPositions);

            // Store formation
            activeFormations[formationId] = formationGroup;

            return true;
        }

        private List<Vector3> CalculateFormationPositions(int unitCount, Vector3 destination, FormationType formation)
        {
            List<Vector3> positions = new List<Vector3>();

            switch (formation)
            {
                case FormationType.Line:
                    positions = CalculateLineFormation(unitCount, destination);
                    break;
                case FormationType.Box:
                    positions = CalculateBoxFormation(unitCount, destination);
                    break;
                case FormationType.Wedge:
                    positions = CalculateWedgeFormation(unitCount, destination);
                    break;
                case FormationType.Column:
                    positions = CalculateColumnFormation(unitCount, destination);
                    break;
                default:
                    positions = CalculateBoxFormation(unitCount, destination);
                    break;
            }

            return positions;
        }

        private List<Vector3> CalculateLineFormation(int unitCount, Vector3 destination)
        {
            List<Vector3> positions = new List<Vector3>();
            float totalWidth = (unitCount - 1) * formationSpacing;
            Vector3 startPos = destination - Vector3.right * (totalWidth * 0.5f);

            for (int i = 0; i < unitCount; i++)
            {
                Vector3 pos = startPos + Vector3.right * (i * formationSpacing);
                positions.Add(pos);
            }

            return positions;
        }

        private List<Vector3> CalculateBoxFormation(int unitCount, Vector3 destination)
        {
            List<Vector3> positions = new List<Vector3>();
            int unitsPerRow = Mathf.CeilToInt(Mathf.Sqrt(unitCount));
            int rows = Mathf.CeilToInt((float)unitCount / unitsPerRow);

            Vector3 startPos = destination - new Vector3(
                (unitsPerRow - 1) * formationSpacing * 0.5f,
                0,
                (rows - 1) * formationDepth * 0.5f
            );

            for (int i = 0; i < unitCount; i++)
            {
                int row = i / unitsPerRow;
                int col = i % unitsPerRow;

                Vector3 pos = startPos + new Vector3(
                    col * formationSpacing,
                    0,
                    row * formationDepth
                );

                positions.Add(pos);
            }

            return positions;
        }

        private List<Vector3> CalculateWedgeFormation(int unitCount, Vector3 destination)
        {
            List<Vector3> positions = new List<Vector3>();
            int currentRow = 0;
            int unitsInCurrentRow = 1;
            int unitsPlaced = 0;

            while (unitsPlaced < unitCount)
            {
                int unitsToPlaceInRow = Mathf.Min(unitsInCurrentRow, unitCount - unitsPlaced);
                float rowWidth = (unitsToPlaceInRow - 1) * formationSpacing;
                Vector3 rowStart = destination - Vector3.right * (rowWidth * 0.5f) - Vector3.forward * (currentRow * formationDepth);

                for (int i = 0; i < unitsToPlaceInRow; i++)
                {
                    Vector3 pos = rowStart + Vector3.right * (i * formationSpacing);
                    positions.Add(pos);
                    unitsPlaced++;
                }

                currentRow++;
                unitsInCurrentRow += 2; // Wedge formation grows by 2 each row
            }

            return positions;
        }

        private List<Vector3> CalculateColumnFormation(int unitCount, Vector3 destination)
        {
            List<Vector3> positions = new List<Vector3>();
            Vector3 startPos = destination + Vector3.forward * ((unitCount - 1) * formationDepth * 0.5f);

            for (int i = 0; i < unitCount; i++)
            {
                Vector3 pos = startPos - Vector3.forward * (i * formationDepth);
                positions.Add(pos);
            }

            return positions;
        }

        private void AssignUnitsToFormationPositions(FormationGroup formation, List<Vector3> positions)
        {
            formation.targetPositions = new Dictionary<Unit, Vector3>();

            // Simple assignment - could be improved with Hungarian algorithm for optimal assignment
            for (int i = 0; i < formation.units.Count && i < positions.Count; i++)
            {
                formation.targetPositions[formation.units[i]] = positions[i];
                formation.units[i].MoveTo(positions[i]);
            }
        }

        private FormationType GetBestFormationForUnits(List<Unit> units)
        {
            if (defaultFormation != FormationType.Auto)
                return defaultFormation;

            if (units.Count <= 3) return FormationType.Line;
            if (units.Count <= 8) return FormationType.Box;
            if (units.Count > maxUnitsPerFormation)
            {
                Debug.LogWarning($"Formation has {units.Count} units, exceeding max of {maxUnitsPerFormation}. Consider splitting into multiple formations.");
            }
            return FormationType.Box;
        }

        private void RemoveUnitFromFormations(Unit unit)
        {
            List<int> formationsToRemove = new List<int>();

            foreach (var kvp in activeFormations)
            {
                var formation = kvp.Value;
                if (formation.units.Contains(unit))
                {
                    formation.units.Remove(unit);
                    if (formation.targetPositions.ContainsKey(unit))
                    {
                        formation.targetPositions.Remove(unit);
                    }

                    // Remove formation if no units left
                    if (formation.units.Count == 0)
                    {
                        formationsToRemove.Add(kvp.Key);
                    }
                }
            }

            foreach (int formationId in formationsToRemove)
            {
                activeFormations.Remove(formationId);
            }
        }

        private void Update()
        {
            UpdateFormations();
        }

        private float lastPathUpdateTime = 0f;

        private void UpdateFormations()
        {
            // Use pathUpdateInterval to limit update frequency
            if (Time.time - lastPathUpdateTime < pathUpdateInterval)
                return;

            lastPathUpdateTime = Time.time;

            List<int> completedFormations = new List<int>();

            foreach (var kvp in activeFormations)
            {
                var formation = kvp.Value;
                bool allUnitsArrived = true;

                foreach (var unit in formation.units)
                {
                    if (unit != null)
                    {
                        // Check if unit has arrived using dynamic threshold
                        if (formation.targetPositions.ContainsKey(unit))
                        {
                            Vector3 targetPos = formation.targetPositions[unit];
                            float distanceToTarget = Vector3.Distance(unit.transform.position, targetPos);
                            float unitArrivalThreshold = GetUnitArrivalThreshold(unit);

                            if (distanceToTarget > unitArrivalThreshold && unit.IsMoving())
                            {
                                allUnitsArrived = false;
                                break;
                            }
                        }
                        else if (unit.IsMoving())
                        {
                            allUnitsArrived = false;
                            break;
                        }
                    }
                }

                if (allUnitsArrived)
                {
                    completedFormations.Add(kvp.Key);
                }
            }

            // Clean up completed formations
            foreach (int formationId in completedFormations)
            {
                activeFormations.Remove(formationId);
            }
        }

        public bool IsValidDestination(Vector3 destination)
        {
            NavMeshHit hit;
            return NavMesh.SamplePosition(destination, out hit, 1f, NavMesh.AllAreas);
        }

        public Vector3 GetNearestValidPosition(Vector3 position)
        {
            NavMeshHit hit;
            if (NavMesh.SamplePosition(position, out hit, 10f, NavMesh.AllAreas))
            {
                return hit.position;
            }
            return position;
        }

        private float GetUnitArrivalThreshold(Unit unit)
        {
            if (unit == null) return arrivalThreshold;

            // Use the unit's collider to determine arrival threshold
            Collider unitCollider = unit.GetComponent<Collider>();
            if (unitCollider != null)
            {
                // Use the largest dimension of the collider bounds + some buffer
                Vector3 size = unitCollider.bounds.size;
                float maxDimension = Mathf.Max(size.x, size.z); // Width or depth
                return maxDimension * 0.75f + 0.5f; // 75% of size + buffer
            }

            // Fallback: try NavMeshAgent radius
            if (unit.NavAgent != null)
            {
                return unit.NavAgent.radius * 2f + 0.5f;
            }

            // Final fallback to configured threshold
            return arrivalThreshold;
        }


    }

    /// <summary>
    /// Represents a group of units moving in formation
    /// </summary>
    [System.Serializable]
    public class FormationGroup
    {
        public int id;
        public List<Unit> units;
        public Vector3 destination;
        public FormationType formation;
        public float spacing;
        public float depth;
        public Dictionary<Unit, Vector3> targetPositions;
    }

    /// <summary>
    /// Different formation types for unit movement
    /// </summary>
    public enum FormationType
    {
        Auto,       // Automatically choose best formation
        Line,       // Single line formation
        Box,        // Rectangular box formation
        Wedge,      // V-shaped wedge formation
        Column      // Single column formation
    }
}
