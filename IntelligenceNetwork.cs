using System.Collections.Generic;
using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Manages intelligence sharing between units, threat assessment, and tactical information propagation
    /// Handles communication networks, intel reports, and coordinated responses
    /// </summary>
    public class IntelligenceNetwork : MonoBehaviour
    {
        [Header("Communication Settings")]
        [SerializeField] private float baseCommRange = 25f;
        [SerializeField] private float intelDecayTime = 30f;
        [SerializeField] private float intelUpdateInterval = 1f;
        [SerializeField] private int maxIntelReports = 50;

        [Header("Threat Assessment")]
        [SerializeField] private bool enableThreatAssessment = true;
        [SerializeField] private float threatUpdateInterval = 2f;
        [SerializeField] private float threatDecayRate = 0.1f;

        [Header("Network Efficiency")]
        [SerializeField] private bool useNetworkRelay = true;
        [SerializeField] private int maxRelayHops = 3;
        [SerializeField] private float relayDelay = 0.5f;

        // Intelligence data
        private Dictionary<Faction, List<IntelReport>> factionIntelligence = new Dictionary<Faction, List<IntelReport>>();
        private Dictionary<Unit, CommunicationNode> communicationNodes = new Dictionary<Unit, CommunicationNode>();
        private Dictionary<Faction, ThreatAssessment> factionThreats = new Dictionary<Faction, ThreatAssessment>();

        // Network tracking
        private List<Unit> allUnits = new List<Unit>();
        private float lastIntelUpdate;
        private float lastThreatUpdate;

        // Singleton pattern
        private static IntelligenceNetwork instance;
        public static IntelligenceNetwork Instance => instance;

        // Events
        public System.Action<IntelReport> OnIntelReportCreated;
        public System.Action<IntelReport> OnIntelReportShared;
        public System.Action<Faction, ThreatLevel> OnThreatLevelChanged;
        public System.Action<Unit, Unit> OnTargetDesignated;

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeNetwork();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            RefreshUnitNetwork();
            SubscribeToEvents();
        }

        private void Update()
        {
            // Update intelligence network
            if (Time.time - lastIntelUpdate >= intelUpdateInterval)
            {
                UpdateIntelligenceNetwork();
                lastIntelUpdate = Time.time;
            }

            // Update threat assessments
            if (enableThreatAssessment && Time.time - lastThreatUpdate >= threatUpdateInterval)
            {
                UpdateThreatAssessments();
                lastThreatUpdate = Time.time;
            }

            // Clean up old intelligence
            CleanupOldIntelligence();
        }

        #region Network Initialization

        private void InitializeNetwork()
        {
            // Initialize faction intelligence dictionaries
            foreach (Faction faction in System.Enum.GetValues(typeof(Faction)))
            {
                factionIntelligence[faction] = new List<IntelReport>();
                factionThreats[faction] = new ThreatAssessment { faction = faction };
            }
        }

        private void RefreshUnitNetwork()
        {
            allUnits.Clear();
            communicationNodes.Clear();

            Unit[] units = FindObjectsByType<Unit>(FindObjectsSortMode.None);
            foreach (Unit unit in units)
            {
                allUnits.Add(unit);
                communicationNodes[unit] = new CommunicationNode
                {
                    unit = unit,
                    communicationRange = GetUnitCommRange(unit),
                    connectedNodes = new List<Unit>()
                };
            }

            // Build communication network connections
            BuildNetworkConnections();
        }

        private void BuildNetworkConnections()
        {
            foreach (var kvp in communicationNodes)
            {
                Unit unit = kvp.Key;
                CommunicationNode node = kvp.Value;
                node.connectedNodes.Clear();

                foreach (Unit otherUnit in allUnits)
                {
                    if (otherUnit == unit) continue;

                    // Only connect units of the same faction
                    if (unit.Faction != otherUnit.Faction) continue;

                    float distance = Vector3.Distance(unit.transform.position, otherUnit.transform.position);
                    if (distance <= node.communicationRange)
                    {
                        node.connectedNodes.Add(otherUnit);
                    }
                }
            }
        }

        #endregion

        #region Intelligence Management

        private void UpdateIntelligenceNetwork()
        {
            // Update network connections (units may have moved)
            BuildNetworkConnections();

            // Process new intelligence from unit detections
            foreach (Unit unit in allUnits)
            {
                if (unit == null || !unit.gameObject.activeInHierarchy) continue;

                ProcessUnitDetections(unit);
            }

            // Propagate intelligence through network
            PropagateIntelligence();
        }

        private void ProcessUnitDetections(Unit observer)
        {
            foreach (Unit detectedEnemy in observer.DetectedEnemies)
            {
                CreateIntelReport(observer, detectedEnemy, IntelType.EnemySighting);
            }

            // Report combat engagements
            if (observer.IsInCombat && observer.CurrentTarget != null)
            {
                CreateIntelReport(observer, observer.CurrentTarget, IntelType.CombatEngagement);
            }
        }

        public void CreateIntelReport(Unit reporter, Unit subject, IntelType intelType, Vector3? customPosition = null)
        {
            if (reporter == null || subject == null) return;

            Vector3 reportPosition = customPosition ?? subject.transform.position;
            
            IntelReport report = new IntelReport
            {
                id = System.Guid.NewGuid().ToString(),
                reporterFaction = reporter.Faction,
                subjectFaction = subject.Faction,
                subjectUnit = subject,
                intelType = intelType,
                position = reportPosition,
                timestamp = Time.time,
                confidence = CalculateIntelConfidence(reporter, subject),
                priority = CalculateIntelPriority(intelType, subject)
            };

            // Add to faction intelligence
            if (!factionIntelligence.ContainsKey(reporter.Faction))
            {
                factionIntelligence[reporter.Faction] = new List<IntelReport>();
            }

            factionIntelligence[reporter.Faction].Add(report);
            
            // Limit number of reports
            if (factionIntelligence[reporter.Faction].Count > maxIntelReports)
            {
                factionIntelligence[reporter.Faction].RemoveAt(0);
            }

            OnIntelReportCreated?.Invoke(report);

            // Share with connected units
            ShareIntelReport(reporter, report);
        }

        private void ShareIntelReport(Unit originator, IntelReport report)
        {
            if (!communicationNodes.ContainsKey(originator)) return;

            HashSet<Unit> notifiedUnits = new HashSet<Unit> { originator };
            Queue<(Unit unit, int hops)> propagationQueue = new Queue<(Unit, int)>();

            // Start propagation from originator's connected nodes
            foreach (Unit connectedUnit in communicationNodes[originator].connectedNodes)
            {
                propagationQueue.Enqueue((connectedUnit, 1));
            }

            while (propagationQueue.Count > 0)
            {
                var (currentUnit, hops) = propagationQueue.Dequeue();

                if (notifiedUnits.Contains(currentUnit) || hops > maxRelayHops) continue;

                notifiedUnits.Add(currentUnit);

                // Notify unit of intelligence
                NotifyUnitOfIntel(currentUnit, report);

                // Continue propagation if using network relay
                if (useNetworkRelay && hops < maxRelayHops && communicationNodes.ContainsKey(currentUnit))
                {
                    foreach (Unit nextUnit in communicationNodes[currentUnit].connectedNodes)
                    {
                        if (!notifiedUnits.Contains(nextUnit))
                        {
                            propagationQueue.Enqueue((nextUnit, hops + 1));
                        }
                    }
                }
            }

            OnIntelReportShared?.Invoke(report);
        }

        private void NotifyUnitOfIntel(Unit unit, IntelReport report)
        {
            // Notify AI units of new intelligence
            EnemyAI enemyAI = unit.GetComponent<EnemyAI>();
            if (enemyAI != null)
            {
                ProcessAIIntelligence(enemyAI, report);
            }

            // Add delay for realistic communication
            if (relayDelay > 0)
            {
                StartCoroutine(DelayedIntelNotification(unit, report));
            }
        }

        private System.Collections.IEnumerator DelayedIntelNotification(Unit unit, IntelReport report)
        {
            yield return new WaitForSeconds(relayDelay);
            // Additional processing can be added here
        }

        private void ProcessAIIntelligence(EnemyAI ai, IntelReport report)
        {
            switch (report.intelType)
            {
                case IntelType.EnemySighting:
                    if (report.subjectUnit != null && FactionManager.Instance.AreFactionsHostile(ai.unit.Faction, report.subjectFaction))
                    {
                        // Investigate or engage based on AI state and distance
                        float distance = Vector3.Distance(ai.transform.position, report.position);
                        if (distance <= ai.unit.DetectionRange * 2f)
                        {
                            ai.InvestigatePosition(report.position);
                        }
                    }
                    break;

                case IntelType.CombatEngagement:
                    if (report.subjectUnit != null && FactionManager.Instance.AreFactionsHostile(ai.unit.Faction, report.subjectFaction))
                    {
                        // Respond to combat if nearby
                        float distance = Vector3.Distance(ai.transform.position, report.position);
                        if (distance <= ai.unit.DetectionRange * 1.5f)
                        {
                            ai.RespondToReinforcement(report.position, report.subjectUnit);
                        }
                    }
                    break;

                case IntelType.ThreatAssessment:
                    // Update threat awareness
                    break;
            }
        }

        #endregion

        #region Threat Assessment

        private void UpdateThreatAssessments()
        {
            foreach (Faction faction in System.Enum.GetValues(typeof(Faction)))
            {
                UpdateFactionThreatAssessment(faction);
            }
        }

        private void UpdateFactionThreatAssessment(Faction faction)
        {
            if (!factionThreats.ContainsKey(faction)) return;

            ThreatAssessment threat = factionThreats[faction];
            ThreatLevel previousLevel = threat.currentThreatLevel;

            // Calculate threat based on recent intelligence
            float threatScore = 0f;
            int recentReports = 0;

            foreach (IntelReport report in factionIntelligence[faction])
            {
                if (Time.time - report.timestamp <= 10f) // Recent reports only
                {
                    threatScore += GetThreatScore(report);
                    recentReports++;
                }
            }

            // Normalize threat score
            if (recentReports > 0)
            {
                threatScore /= recentReports;
            }

            // Apply decay
            threat.threatScore = Mathf.Lerp(threat.threatScore, threatScore, threatDecayRate);

            // Determine threat level
            ThreatLevel newLevel = DetermineThreatLevel(threat.threatScore);
            
            if (newLevel != previousLevel)
            {
                threat.currentThreatLevel = newLevel;
                OnThreatLevelChanged?.Invoke(faction, newLevel);
            }
        }

        private float GetThreatScore(IntelReport report)
        {
            float score = 0f;

            switch (report.intelType)
            {
                case IntelType.EnemySighting:
                    score = 0.3f;
                    break;
                case IntelType.CombatEngagement:
                    score = 0.8f;
                    break;
                case IntelType.ThreatAssessment:
                    score = 0.5f;
                    break;
            }

            // Modify by priority and confidence
            score *= report.priority * report.confidence;

            return score;
        }

        private ThreatLevel DetermineThreatLevel(float threatScore)
        {
            if (threatScore >= 0.8f) return ThreatLevel.Critical;
            if (threatScore >= 0.6f) return ThreatLevel.High;
            if (threatScore >= 0.4f) return ThreatLevel.Medium;
            if (threatScore >= 0.2f) return ThreatLevel.Low;
            return ThreatLevel.None;
        }

        #endregion

        #region Helper Methods

        private float GetUnitCommRange(Unit unit)
        {
            if (FactionManager.Instance != null)
            {
                var factionData = FactionManager.Instance.GetFactionData(unit.Faction);
                if (factionData != null)
                {
                    return factionData.communicationRange;
                }
            }
            return baseCommRange;
        }

        private float CalculateIntelConfidence(Unit reporter, Unit subject)
        {
            float distance = Vector3.Distance(reporter.transform.position, subject.transform.position);
            float maxRange = reporter.DetectionRange;
            
            // Confidence decreases with distance
            float confidence = 1f - (distance / maxRange);
            return Mathf.Clamp01(confidence);
        }

        private float CalculateIntelPriority(IntelType intelType, Unit subject)
        {
            float priority = 0.5f;

            switch (intelType)
            {
                case IntelType.EnemySighting:
                    priority = 0.6f;
                    break;
                case IntelType.CombatEngagement:
                    priority = 0.9f;
                    break;
                case IntelType.ThreatAssessment:
                    priority = 0.7f;
                    break;
            }

            // Modify by unit type importance
            switch (subject.UnitType)
            {
                case UnitType.Tank:
                case UnitType.Artillery:
                    priority *= 1.2f;
                    break;
                case UnitType.Aircraft:
                    priority *= 1.5f;
                    break;
            }

            return Mathf.Clamp01(priority);
        }

        private void PropagateIntelligence()
        {
            // Additional intelligence propagation logic can be added here
            // This could include cross-referencing reports, updating confidence levels, etc.
        }

        private void CleanupOldIntelligence()
        {
            foreach (var kvp in factionIntelligence)
            {
                List<IntelReport> reports = kvp.Value;
                for (int i = reports.Count - 1; i >= 0; i--)
                {
                    if (Time.time - reports[i].timestamp > intelDecayTime)
                    {
                        reports.RemoveAt(i);
                    }
                }
            }
        }

        private void SubscribeToEvents()
        {
            // Subscribe to unit events for automatic intelligence gathering
            if (VisionSystem.Instance != null)
            {
                VisionSystem.Instance.OnUnitSpotted += OnUnitSpotted;
            }
        }

        private void OnUnitSpotted(Unit observer, Unit spotted)
        {
            if (FactionManager.Instance.AreFactionsHostile(observer.Faction, spotted.Faction))
            {
                CreateIntelReport(observer, spotted, IntelType.EnemySighting);
            }
        }

        #endregion

        #region Public Interface

        /// <summary>
        /// Get all intelligence reports for a faction
        /// </summary>
        public List<IntelReport> GetFactionIntelligence(Faction faction)
        {
            if (factionIntelligence.ContainsKey(faction))
            {
                return new List<IntelReport>(factionIntelligence[faction]);
            }
            return new List<IntelReport>();
        }

        /// <summary>
        /// Get current threat level for a faction
        /// </summary>
        public ThreatLevel GetFactionThreatLevel(Faction faction)
        {
            if (factionThreats.ContainsKey(faction))
            {
                return factionThreats[faction].currentThreatLevel;
            }
            return ThreatLevel.None;
        }

        /// <summary>
        /// Manually create an intelligence report
        /// </summary>
        public void ReportIntelligence(Unit reporter, Vector3 position, IntelType intelType, Faction subjectFaction)
        {
            IntelReport report = new IntelReport
            {
                id = System.Guid.NewGuid().ToString(),
                reporterFaction = reporter.Faction,
                subjectFaction = subjectFaction,
                intelType = intelType,
                position = position,
                timestamp = Time.time,
                confidence = 0.8f,
                priority = CalculateIntelPriority(intelType, null)
            };

            if (!factionIntelligence.ContainsKey(reporter.Faction))
            {
                factionIntelligence[reporter.Faction] = new List<IntelReport>();
            }

            factionIntelligence[reporter.Faction].Add(report);
            ShareIntelReport(reporter, report);
        }

        /// <summary>
        /// Check if two units can communicate
        /// </summary>
        public bool CanCommunicate(Unit unit1, Unit unit2)
        {
            if (unit1.Faction != unit2.Faction) return false;
            
            if (communicationNodes.ContainsKey(unit1) && communicationNodes.ContainsKey(unit2))
            {
                return communicationNodes[unit1].connectedNodes.Contains(unit2);
            }
            
            return false;
        }

        #endregion
    }

    /// <summary>
    /// Intelligence report data structure
    /// </summary>
    [System.Serializable]
    public class IntelReport
    {
        public string id;
        public Faction reporterFaction;
        public Faction subjectFaction;
        public Unit subjectUnit;
        public IntelType intelType;
        public Vector3 position;
        public float timestamp;
        public float confidence; // 0-1 reliability of the report
        public float priority;   // 0-1 importance of the report
    }

    /// <summary>
    /// Communication node for network connectivity
    /// </summary>
    [System.Serializable]
    public class CommunicationNode
    {
        public Unit unit;
        public float communicationRange;
        public List<Unit> connectedNodes;
    }

    /// <summary>
    /// Threat assessment data for a faction
    /// </summary>
    [System.Serializable]
    public class ThreatAssessment
    {
        public Faction faction;
        public ThreatLevel currentThreatLevel;
        public float threatScore;
        public float lastUpdate;
    }

    /// <summary>
    /// Types of intelligence reports
    /// </summary>
    public enum IntelType
    {
        EnemySighting,      // Enemy unit spotted
        CombatEngagement,   // Combat in progress
        ThreatAssessment,   // General threat evaluation
        UnitDestroyed,      // Friendly unit destroyed
        ObjectiveUpdate     // Mission objective status
    }

    /// <summary>
    /// Threat level classifications
    /// </summary>
    public enum ThreatLevel
    {
        None,       // No threats detected
        Low,        // Minor threats present
        Medium,     // Moderate threat level
        High,       // Significant threats
        Critical    // Immediate danger
    }
