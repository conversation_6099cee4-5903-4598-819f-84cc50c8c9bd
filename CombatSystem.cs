using System.Collections.Generic;
using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Manages combat mechanics, weapon systems, and projectiles
    /// Handles damage calculation, ballistics, and combat effects
    /// </summary>
    public class CombatSystem : MonoBehaviour
    {
        [Header("Projectile Settings")]
        [SerializeField] private GameObject defaultProjectilePrefab;
        [SerializeField] private float projectileLifetime = 5f;
        [SerializeField] private LayerMask projectileCollisionMask = -1;

        [Header("Combat Effects")]
        [SerializeField] private GameObject muzzleFlashPrefab;
        [SerializeField] private GameObject hitEffectPrefab;
        [SerializeField] private GameObject explosionEffectPrefab;

        [Header("Audio")]
        [SerializeField] private AudioClip[] weaponFireSounds;
        [SerializeField] private AudioClip[] impactSounds;
        [SerializeField] private AudioSource audioSource;

        // Active projectiles tracking
        private List<Projectile> activeProjectiles = new List<Projectile>();
        
        // Singleton pattern
        private static CombatSystem instance;
        public static CombatSystem Instance => instance;

        // Events
        public System.Action<Unit, Unit, int> OnDamageDealt;
        public System.Action<Unit> OnUnitKilled;
        public System.Action<Vector3, float> OnExplosion;

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }

            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }

        private void Update()
        {
            UpdateProjectiles();
        }

        /// <summary>
        /// Fire a projectile from attacker to target
        /// </summary>
        public void FireProjectile(Unit attacker, Unit target, WeaponData weaponData)
        {
            if (attacker == null || target == null || weaponData == null) return;

            Vector3 firePosition = GetFirePosition(attacker);
            Vector3 targetPosition = GetTargetPosition(target);

            // Create projectile
            GameObject projectileObj = CreateProjectile(weaponData.projectilePrefab ?? defaultProjectilePrefab, firePosition);
            Projectile projectile = projectileObj.GetComponent<Projectile>();
            
            if (projectile == null)
            {
                projectile = projectileObj.AddComponent<Projectile>();
            }

            // Initialize projectile with lifetime
            projectile.Initialize(attacker, target, targetPosition, weaponData);
            activeProjectiles.Add(projectile);

            // Set projectile lifetime
            Destroy(projectileObj, projectileLifetime);

            // Play effects
            PlayMuzzleFlash(firePosition, (targetPosition - firePosition).normalized);
            PlayWeaponSound(weaponData.weaponType);
        }

        /// <summary>
        /// Fire an instant hit weapon (hitscan)
        /// </summary>
        public void FireInstantHit(Unit attacker, Unit target, WeaponData weaponData)
        {
            if (attacker == null || target == null || weaponData == null) return;

            Vector3 firePosition = GetFirePosition(attacker);
            Vector3 targetPosition = GetTargetPosition(target);
            Vector3 direction = (targetPosition - firePosition).normalized;

            // Perform raycast
            if (Physics.Raycast(firePosition, direction, out RaycastHit hit, weaponData.range, projectileCollisionMask))
            {
                Unit hitUnit = hit.collider.GetComponent<Unit>();
                if (hitUnit != null)
                {
                    ApplyDamage(attacker, hitUnit, weaponData.damage, hit.point);
                }
                else
                {
                    // Hit terrain or obstacle
                    PlayHitEffect(hit.point, hit.normal);
                }
            }

            // Play effects
            PlayMuzzleFlash(firePosition, direction);
            PlayWeaponSound(weaponData.weaponType);
        }

        /// <summary>
        /// Apply damage to a target unit
        /// </summary>
        public void ApplyDamage(Unit attacker, Unit target, int damage, Vector3 hitPoint)
        {
            if (target == null) return;

            // Check if damage should be applied based on faction relationships
            if (attacker != null && FactionManager.Instance != null)
            {
                if (!FactionManager.Instance.ShouldApplyDamage(attacker.Faction, target.Faction))
                {
                    return;
                }
            }

            int finalDamage = CalculateDamage(attacker, target, damage);
            
            // Apply damage
            target.TakeDamage(finalDamage, attacker);

            // Play hit effect
            PlayHitEffect(hitPoint, Vector3.up);

            // Trigger events
            OnDamageDealt?.Invoke(attacker, target, finalDamage);

            if (target.CurrentHealth <= 0)
            {
                OnUnitKilled?.Invoke(target);
            }
        }

        /// <summary>
        /// Create an explosion at the specified position
        /// </summary>
        public void CreateExplosion(Vector3 position, float radius, int damage, Unit attacker = null)
        {
            // Find all units in explosion radius
            Collider[] hitColliders = Physics.OverlapSphere(position, radius);
            
            foreach (Collider col in hitColliders)
            {
                Unit unit = col.GetComponent<Unit>();
                if (unit != null)
                {
                    // Calculate damage based on distance
                    float distance = Vector3.Distance(position, unit.transform.position);
                    float damageMultiplier = 1f - (distance / radius);
                    int explosionDamage = Mathf.RoundToInt(damage * damageMultiplier);
                    
                    if (explosionDamage > 0)
                    {
                        ApplyDamage(attacker, unit, explosionDamage, unit.transform.position);
                    }
                }
            }

            // Play explosion effect
            PlayExplosionEffect(position);
            OnExplosion?.Invoke(position, radius);
        }

        private void UpdateProjectiles()
        {
            for (int i = activeProjectiles.Count - 1; i >= 0; i--)
            {
                if (activeProjectiles[i] == null || !activeProjectiles[i].gameObject.activeInHierarchy)
                {
                    activeProjectiles.RemoveAt(i);
                }
            }
        }

        private GameObject CreateProjectile(GameObject prefab, Vector3 position)
        {
            if (prefab == null)
            {
                // Create a simple sphere projectile
                GameObject projectile = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                projectile.transform.localScale = Vector3.one * 0.1f;
                projectile.transform.position = position;
                
                // Add a simple material
                Renderer renderer = projectile.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material.color = Color.yellow;
                }
                
                return projectile;
            }
            else
            {
                return Instantiate(prefab, position, Quaternion.identity);
            }
        }

        private Vector3 GetFirePosition(Unit unit)
        {
            // Try to find a weapon mount point, otherwise use unit center + height offset
            Transform weaponMount = unit.transform.Find("WeaponMount");
            if (weaponMount != null)
            {
                return weaponMount.position;
            }
            
            return unit.transform.position + Vector3.up * 1.5f;
        }

        private Vector3 GetTargetPosition(Unit target)
        {
            // Aim for center mass
            Bounds bounds = target.GetSelectionBounds();
            return bounds.center;
        }

        private int CalculateDamage(Unit attacker, Unit target, int baseDamage)
        {
            // Basic damage calculation - can be enhanced with armor, unit type modifiers, etc.
            float damageMultiplier = 1f;
            
            // Unit type effectiveness (example: tanks take less damage from small arms)
            if (attacker != null)
            {
                damageMultiplier *= GetUnitTypeEffectiveness(attacker.UnitType, target.UnitType);
            }
            
            return Mathf.RoundToInt(baseDamage * damageMultiplier);
        }

        private float GetUnitTypeEffectiveness(UnitType attackerType, UnitType targetType)
        {
            // Example effectiveness matrix
            switch (attackerType)
            {
                case UnitType.Infantry:
                    return targetType == UnitType.Tank ? 0.5f : 1f;
                case UnitType.Tank:
                    return targetType == UnitType.Infantry ? 1.5f : 1f;
                case UnitType.Artillery:
                    return targetType == UnitType.Building ? 2f : 1.2f;
                default:
                    return 1f;
            }
        }

        private void PlayMuzzleFlash(Vector3 position, Vector3 direction)
        {
            if (muzzleFlashPrefab != null)
            {
                GameObject flash = Instantiate(muzzleFlashPrefab, position, Quaternion.LookRotation(direction));
                Destroy(flash, 0.5f);
            }
        }

        private void PlayHitEffect(Vector3 position, Vector3 normal)
        {
            if (hitEffectPrefab != null)
            {
                GameObject effect = Instantiate(hitEffectPrefab, position, Quaternion.LookRotation(normal));
                Destroy(effect, 2f);
            }
        }

        private void PlayExplosionEffect(Vector3 position)
        {
            if (explosionEffectPrefab != null)
            {
                GameObject explosion = Instantiate(explosionEffectPrefab, position, Quaternion.identity);
                Destroy(explosion, 5f);
            }
        }

        private void PlayWeaponSound(WeaponType weaponType)
        {
            if (weaponFireSounds != null && weaponFireSounds.Length > 0 && audioSource != null)
            {
                AudioClip clip = weaponFireSounds[Random.Range(0, weaponFireSounds.Length)];
                audioSource.PlayOneShot(clip);
            }
        }

        /// <summary>
        /// Remove a projectile from tracking
        /// </summary>
        public void RemoveProjectile(Projectile projectile)
        {
            activeProjectiles.Remove(projectile);
        }
    }

    /// <summary>
    /// Data structure for weapon configuration
    /// </summary>
    [System.Serializable]
    public class WeaponData
    {
        public string weaponName = "Basic Weapon";
        public WeaponType weaponType = WeaponType.Ballistic;
        public int damage = 25;
        public float range = 15f;
        public float fireRate = 1f; // Shots per second
        public float projectileSpeed = 20f;
        public float accuracy = 0.95f; // 0-1 accuracy rating
        public bool isExplosive = false;
        public float explosionRadius = 0f;
        public GameObject projectilePrefab;
    }

    /// <summary>
    /// Types of weapons available
    /// </summary>
    public enum WeaponType
    {
        Ballistic,      // Bullets, shells
        Energy,         // Lasers, plasma
        Explosive,      // Rockets, grenades
        Melee          // Close combat weapons
    }
}
