using System.Collections.Generic;
using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Manages command structure, officer hierarchy, and coordinated unit responses
    /// Handles chain of command, command authority, and tactical coordination
    /// </summary>
    public class CommandHierarchy : MonoBehaviour
    {
        [Header("Command Structure")]
        [SerializeField] private bool enableCommandHierarchy = true;
        [SerializeField] private float commandRange = 30f;
        [SerializeField] private float commandUpdateInterval = 2f;
        [SerializeField] private int maxSubordinates = 8;

        [Header("Officer Settings")]
        [SerializeField] private float officerBonusRange = 1.5f;
        [SerializeField] private float officerEfficiencyBonus = 0.2f;
        [SerializeField] private bool allowChainOfCommand = true;

        [Header("Coordination")]
        [SerializeField] private float coordinationRadius = 20f;
        [SerializeField] private float responseTime = 1f;
        [SerializeField] private bool enableGroupTactics = true;

        // Command structure data
        private Dictionary<Unit, CommandNode> commandNodes = new Dictionary<Unit, CommandNode>();
        private Dictionary<Faction, List<Unit>> factionOfficers = new Dictionary<Faction, List<Unit>>();
        private Dictionary<Faction, CommandStructure> factionCommands = new Dictionary<Faction, CommandStructure>();

        // Coordination tracking
        private List<TacticalOrder> activeOrders = new List<TacticalOrder>();
        private float lastCommandUpdate;

        // Singleton pattern
        private static CommandHierarchy instance;
        public static CommandHierarchy Instance => instance;

        // Events
        public System.Action<Unit, Unit> OnCommandAssigned;
        public System.Action<Unit, Unit> OnCommandRevoked;
        public System.Action<TacticalOrder> OnTacticalOrderIssued;
        public System.Action<TacticalOrder> OnTacticalOrderCompleted;

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeCommandStructure();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            RefreshCommandStructure();
        }

        private void Update()
        {
            if (!enableCommandHierarchy) return;

            // Update command structure periodically
            if (Time.time - lastCommandUpdate >= commandUpdateInterval)
            {
                UpdateCommandStructure();
                lastCommandUpdate = Time.time;
            }

            // Process active tactical orders
            ProcessTacticalOrders();
        }

        #region Command Structure Management

        private void InitializeCommandStructure()
        {
            foreach (Faction faction in System.Enum.GetValues(typeof(Faction)))
            {
                factionOfficers[faction] = new List<Unit>();
                factionCommands[faction] = new CommandStructure { faction = faction };
            }
        }

        private void RefreshCommandStructure()
        {
            commandNodes.Clear();
            foreach (var list in factionOfficers.Values)
            {
                list.Clear();
            }

            Unit[] allUnits = FindObjectsByType<Unit>(FindObjectsSortMode.None);
            
            foreach (Unit unit in allUnits)
            {
                if (unit == null) continue;

                // Create command node for each unit
                CommandNode node = new CommandNode
                {
                    unit = unit,
                    rank = DetermineUnitRank(unit),
                    commandRange = GetUnitCommandRange(unit),
                    subordinates = new List<Unit>(),
                    superior = null
                };

                commandNodes[unit] = node;

                // Add officers to faction lists
                if (node.rank >= CommandRank.Sergeant)
                {
                    factionOfficers[unit.Faction].Add(unit);
                }
            }

            // Build command relationships
            BuildCommandRelationships();
        }

        private void BuildCommandRelationships()
        {
            foreach (Faction faction in System.Enum.GetValues(typeof(Faction)))
            {
                BuildFactionCommandStructure(faction);
            }
        }

        private void BuildFactionCommandStructure(Faction faction)
        {
            List<Unit> factionUnits = GetFactionUnits(faction);
            List<Unit> officers = factionOfficers[faction];

            // Sort officers by rank (highest first)
            officers.Sort((a, b) => commandNodes[b].rank.CompareTo(commandNodes[a].rank));

            foreach (Unit unit in factionUnits)
            {
                if (officers.Contains(unit)) continue; // Officers don't need superiors in this simple system

                CommandNode unitNode = commandNodes[unit];
                Unit bestOfficer = FindBestOfficer(unit, officers);

                if (bestOfficer != null)
                {
                    AssignCommand(bestOfficer, unit);
                }
            }
        }

        private Unit FindBestOfficer(Unit subordinate, List<Unit> officers)
        {
            Unit bestOfficer = null;
            float bestScore = float.MaxValue;

            foreach (Unit officer in officers)
            {
                CommandNode officerNode = commandNodes[officer];
                
                // Check if officer has capacity
                if (officerNode.subordinates.Count >= maxSubordinates) continue;

                // Check range using safe method
                float distance = Vector3.Distance(subordinate.transform.position, officer.transform.position);
                float officerRange = GetUnitCommandRange(officer);
                if (distance > officerRange) continue;

                // Calculate score (prefer closer officers with higher rank)
                float score = distance / (float)(officerNode.rank + 1);
                
                if (score < bestScore)
                {
                    bestScore = score;
                    bestOfficer = officer;
                }
            }

            return bestOfficer;
        }

        private void AssignCommand(Unit superior, Unit subordinate)
        {
            if (!commandNodes.ContainsKey(superior) || !commandNodes.ContainsKey(subordinate)) return;

            CommandNode superiorNode = commandNodes[superior];
            CommandNode subordinateNode = commandNodes[subordinate];

            // Remove from previous superior if any
            if (subordinateNode.superior != null)
            {
                RevokeCommand(subordinateNode.superior, subordinate);
            }

            // Assign new command relationship
            superiorNode.subordinates.Add(subordinate);
            subordinateNode.superior = superior;

            OnCommandAssigned?.Invoke(superior, subordinate);
        }

        private void RevokeCommand(Unit superior, Unit subordinate)
        {
            if (!commandNodes.ContainsKey(superior) || !commandNodes.ContainsKey(subordinate)) return;

            CommandNode superiorNode = commandNodes[superior];
            CommandNode subordinateNode = commandNodes[subordinate];

            superiorNode.subordinates.Remove(subordinate);
            subordinateNode.superior = null;

            OnCommandRevoked?.Invoke(superior, subordinate);
        }

        private void UpdateCommandStructure()
        {
            // Refresh command relationships based on current positions
            RefreshCommandStructure();

            // Update command effectiveness
            UpdateCommandEffectiveness();
        }

        private void UpdateCommandEffectiveness()
        {
            foreach (var kvp in commandNodes)
            {
                Unit unit = kvp.Key;
                CommandNode node = kvp.Value;

                // Apply command bonuses
                if (node.superior != null)
                {
                    ApplyCommandBonus(unit, node.superior);
                }
            }
        }

        #endregion

        #region Tactical Orders

        public void IssueTacticalOrder(Unit commander, TacticalOrderType orderType, Vector3 targetPosition, List<Unit> targetUnits = null)
        {
            if (!commandNodes.ContainsKey(commander)) return;

            List<Unit> assignedUnits = GetCommanderSubordinates(commander);

            // If using chain of command, propagate through hierarchy
            if (allowChainOfCommand)
            {
                assignedUnits = GetAllSubordinatesInChain(commander);
            }

            TacticalOrder order = new TacticalOrder
            {
                id = System.Guid.NewGuid().ToString(),
                commander = commander,
                orderType = orderType,
                targetPosition = targetPosition,
                targetUnits = targetUnits ?? new List<Unit>(),
                assignedUnits = assignedUnits,
                issueTime = Time.time,
                status = OrderStatus.Issued
            };

            activeOrders.Add(order);
            ExecuteTacticalOrder(order);
            OnTacticalOrderIssued?.Invoke(order);
        }

        private List<Unit> GetAllSubordinatesInChain(Unit commander)
        {
            List<Unit> allSubordinates = new List<Unit>();
            Queue<Unit> toProcess = new Queue<Unit>();
            toProcess.Enqueue(commander);

            while (toProcess.Count > 0)
            {
                Unit current = toProcess.Dequeue();
                if (commandNodes.ContainsKey(current))
                {
                    foreach (Unit subordinate in commandNodes[current].subordinates)
                    {
                        if (!allSubordinates.Contains(subordinate))
                        {
                            allSubordinates.Add(subordinate);
                            toProcess.Enqueue(subordinate);
                        }
                    }
                }
            }

            return allSubordinates;
        }

        private void ExecuteTacticalOrder(TacticalOrder order)
        {
            switch (order.orderType)
            {
                case TacticalOrderType.Attack:
                    ExecuteAttackOrder(order);
                    break;
                case TacticalOrderType.Defend:
                    ExecuteDefendOrder(order);
                    break;
                case TacticalOrderType.Regroup:
                    ExecuteRegroupOrder(order);
                    break;
                case TacticalOrderType.Retreat:
                    ExecuteRetreatOrder(order);
                    break;
                case TacticalOrderType.Patrol:
                    ExecutePatrolOrder(order);
                    break;
            }

            order.status = OrderStatus.InProgress;
        }

        private void ExecuteAttackOrder(TacticalOrder order)
        {
            if (enableGroupTactics && order.assignedUnits.Count > 1)
            {
                // Coordinate group attack with timing
                StartCoroutine(CoordinatedAttack(order));
            }
            else
            {
                // Individual unit attacks
                foreach (Unit unit in order.assignedUnits)
                {
                    if (unit == null) continue;

                    // Find target or move to position
                    if (order.targetUnits.Count > 0)
                    {
                        Unit target = GetClosestTarget(unit, order.targetUnits);
                        if (target != null)
                        {
                            unit.AttackTarget(target);
                        }
                    }
                    else
                    {
                        unit.MoveTo(order.targetPosition);
                    }
                }
            }
        }

        private System.Collections.IEnumerator CoordinatedAttack(TacticalOrder order)
        {
            // Wait for response time to coordinate
            yield return new WaitForSeconds(responseTime);

            // Execute coordinated attack
            foreach (Unit unit in order.assignedUnits)
            {
                if (unit == null) continue;

                if (order.targetUnits.Count > 0)
                {
                    Unit target = GetClosestTarget(unit, order.targetUnits);
                    if (target != null)
                    {
                        unit.AttackTarget(target);
                    }
                }
                else
                {
                    unit.MoveTo(order.targetPosition);
                }
            }
        }

        private void ExecuteDefendOrder(TacticalOrder order)
        {
            foreach (Unit unit in order.assignedUnits)
            {
                if (unit == null) continue;

                // Move to defensive position
                Vector3 defensivePosition = CalculateDefensivePosition(unit, order.targetPosition);
                unit.MoveTo(defensivePosition);
            }
        }

        private void ExecuteRegroupOrder(TacticalOrder order)
        {
            foreach (Unit unit in order.assignedUnits)
            {
                if (unit == null) continue;

                // Move to rally point
                Vector3 rallyPoint = CalculateRallyPoint(unit, order.targetPosition);
                unit.MoveTo(rallyPoint);
            }
        }

        private void ExecuteRetreatOrder(TacticalOrder order)
        {
            foreach (Unit unit in order.assignedUnits)
            {
                if (unit == null) continue;

                // Calculate retreat position
                Vector3 retreatPosition = CalculateRetreatPosition(unit, order.targetPosition);
                unit.MoveTo(retreatPosition);
            }
        }

        private void ExecutePatrolOrder(TacticalOrder order)
        {
            // Set up patrol routes for assigned units
            foreach (Unit unit in order.assignedUnits)
            {
                if (unit == null) continue;

                EnemyAI ai = unit.GetComponent<EnemyAI>();
                if (ai != null)
                {
                    ai.InvestigatePosition(order.targetPosition);
                }
                else
                {
                    unit.MoveTo(order.targetPosition);
                }
            }
        }

        private void ProcessTacticalOrders()
        {
            for (int i = activeOrders.Count - 1; i >= 0; i--)
            {
                TacticalOrder order = activeOrders[i];
                
                if (IsTacticalOrderComplete(order))
                {
                    order.status = OrderStatus.Completed;
                    OnTacticalOrderCompleted?.Invoke(order);
                    activeOrders.RemoveAt(i);
                }
                else if (Time.time - order.issueTime > 60f) // Timeout after 60 seconds
                {
                    order.status = OrderStatus.Failed;
                    activeOrders.RemoveAt(i);
                }
            }
        }

        private bool IsTacticalOrderComplete(TacticalOrder order)
        {
            switch (order.orderType)
            {
                case TacticalOrderType.Attack:
                    return order.targetUnits.Count == 0 || order.targetUnits.TrueForAll(u => u == null || u.CurrentHealth <= 0);
                
                case TacticalOrderType.Regroup:
                    return order.assignedUnits.TrueForAll(u => u == null || 
                        Vector3.Distance(u.transform.position, order.targetPosition) < 5f);
                
                default:
                    return false; // Other orders are ongoing
            }
        }

        #endregion

        #region Helper Methods

        private CommandRank DetermineUnitRank(Unit unit)
        {
            // Simple rank assignment based on unit type
            // In a real game, this would be based on unit data or components
            switch (unit.UnitType)
            {
                case UnitType.Infantry:
                    return CommandRank.Private;
                case UnitType.LightVehicle:
                    return CommandRank.Corporal;
                case UnitType.HeavyVehicle:
                case UnitType.Tank:
                    return CommandRank.Sergeant;
                case UnitType.Artillery:
                    return CommandRank.Lieutenant;
                case UnitType.Aircraft:
                    return CommandRank.Captain;
                default:
                    return CommandRank.Private;
            }
        }

        private float GetUnitCommandRange(Unit unit)
        {
            if (!commandNodes.ContainsKey(unit))
            {
                return commandRange; // Return default if unit not in dictionary
            }

            CommandRank rank = commandNodes[unit].rank;
            float baseRange = commandRange;

            // Higher ranks have larger command ranges
            switch (rank)
            {
                case CommandRank.Sergeant:
                    return baseRange * 1.2f;
                case CommandRank.Lieutenant:
                    return baseRange * 1.5f;
                case CommandRank.Captain:
                    return baseRange * 2f;
                default:
                    return baseRange;
            }
        }

        private List<Unit> GetFactionUnits(Faction faction)
        {
            List<Unit> units = new List<Unit>();
            foreach (var kvp in commandNodes)
            {
                if (kvp.Key.Faction == faction)
                {
                    units.Add(kvp.Key);
                }
            }
            return units;
        }

        private List<Unit> GetCommanderSubordinates(Unit commander)
        {
            if (commandNodes.ContainsKey(commander))
            {
                return new List<Unit>(commandNodes[commander].subordinates);
            }
            return new List<Unit>();
        }

        private void ApplyCommandBonus(Unit subordinate, Unit superior)
        {
            // Apply command bonuses based on officer presence
            CommandNode superiorNode = commandNodes[superior];

            if (superiorNode.rank >= CommandRank.Sergeant)
            {
                // Apply efficiency bonus within officer range
                float distance = Vector3.Distance(subordinate.transform.position, superior.transform.position);
                if (distance <= officerBonusRange * GetUnitCommandRange(superior))
                {
                    // Bonus could be applied to accuracy, detection range, etc.
                    // For now, this is a framework for future enhancements
                    float bonus = officerEfficiencyBonus * (float)superiorNode.rank;
                    // Apply bonus to subordinate's capabilities
                }
            }
        }

        private Unit GetClosestTarget(Unit unit, List<Unit> targets)
        {
            Unit closest = null;
            float closestDistance = float.MaxValue;

            foreach (Unit target in targets)
            {
                if (target == null) continue;

                float distance = Vector3.Distance(unit.transform.position, target.transform.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closest = target;
                }
            }

            return closest;
        }

        private Vector3 CalculateDefensivePosition(Unit unit, Vector3 centerPosition)
        {
            // Calculate a defensive position around the center using coordination radius
            Vector3 direction = (unit.transform.position - centerPosition).normalized;
            float distance = Mathf.Min(coordinationRadius * 0.5f, 10f);
            return centerPosition + direction * distance;
        }

        private Vector3 CalculateRallyPoint(Unit unit, Vector3 centerPosition)
        {
            // Simple rally point calculation
            return centerPosition + Random.insideUnitSphere * 5f;
        }

        private Vector3 CalculateRetreatPosition(Unit unit, Vector3 safePosition)
        {
            // Calculate retreat position towards safe area
            Vector3 direction = (safePosition - unit.transform.position).normalized;
            return unit.transform.position + direction * 20f;
        }

        #endregion

        #region Public Interface

        /// <summary>
        /// Get the superior officer of a unit
        /// </summary>
        public Unit GetSuperior(Unit unit)
        {
            if (commandNodes.ContainsKey(unit))
            {
                return commandNodes[unit].superior;
            }
            return null;
        }

        /// <summary>
        /// Get all subordinates of a unit
        /// </summary>
        public List<Unit> GetSubordinates(Unit unit)
        {
            return GetCommanderSubordinates(unit);
        }

        /// <summary>
        /// Check if one unit commands another
        /// </summary>
        public bool IsInChainOfCommand(Unit superior, Unit subordinate)
        {
            if (!commandNodes.ContainsKey(subordinate)) return false;

            Unit current = commandNodes[subordinate].superior;
            while (current != null)
            {
                if (current == superior) return true;
                current = commandNodes.ContainsKey(current) ? commandNodes[current].superior : null;
            }

            return false;
        }

        /// <summary>
        /// Get the rank of a unit
        /// </summary>
        public CommandRank GetUnitRank(Unit unit)
        {
            if (commandNodes.ContainsKey(unit))
            {
                return commandNodes[unit].rank;
            }
            return CommandRank.Private;
        }

        #endregion
    }

    /// <summary>
    /// Command node representing a unit's position in the hierarchy
    /// </summary>
    [System.Serializable]
    public class CommandNode
    {
        public Unit unit;
        public CommandRank rank;
        public float commandRange;
        public List<Unit> subordinates;
        public Unit superior;
    }

    /// <summary>
    /// Command structure for a faction
    /// </summary>
    [System.Serializable]
    public class CommandStructure
    {
        public Faction faction;
        public List<Unit> officers;
        public Dictionary<CommandRank, List<Unit>> rankStructure;
    }

    /// <summary>
    /// Tactical order data structure
    /// </summary>
    [System.Serializable]
    public class TacticalOrder
    {
        public string id;
        public Unit commander;
        public TacticalOrderType orderType;
        public Vector3 targetPosition;
        public List<Unit> targetUnits;
        public List<Unit> assignedUnits;
        public float issueTime;
        public OrderStatus status;
    }

    /// <summary>
    /// Command ranks in the hierarchy
    /// </summary>
    public enum CommandRank
    {
        Private = 0,
        Corporal = 1,
        Sergeant = 2,
        Lieutenant = 3,
        Captain = 4,
        Major = 5,
        Colonel = 6
    }

    /// <summary>
    /// Types of tactical orders
    /// </summary>
    public enum TacticalOrderType
    {
        Attack,     // Engage enemy targets
        Defend,     // Hold position and defend
        Regroup,    // Rally to a position
        Retreat,    // Withdraw to safety
        Patrol,     // Patrol an area
        Support     // Support other units
    }

    /// <summary>
    /// Status of tactical orders
    /// </summary>
    public enum OrderStatus
    {
        Issued,     // Order has been given
        InProgress, // Order is being executed
        Completed,  // Order successfully completed
        Failed,     // Order failed to complete
        Cancelled   // Order was cancelled
    }
}
