using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Handles projectile movement, collision detection, and damage application
    /// Supports ballistic trajectories, homing projectiles, and explosive warheads
    /// </summary>
    [RequireComponent(typeof(Rigidbody))]
    public class Projectile : MonoBehaviour
    {
        [Header("Projectile Settings")]
        [SerializeField] private float speed = 20f;
        [SerializeField] private float lifetime = 5f;
        [SerializeField] private bool useGravity = true;
        [SerializeField] private bool isHoming = false;
        [SerializeField] private float homingStrength = 2f;

        [Header("Visual")]
        [SerializeField] private TrailRenderer trailRenderer;
        [SerializeField] private Light projectileLight;

        // Projectile data
        private Unit attacker;
        private Unit target;
        private Vector3 targetPosition;
        private WeaponData weaponData;
        private Rigidbody rb;
        private float spawnTime;
        private bool hasHit = false;

        // Ballistics
        private Vector3 initialVelocity;
        private float gravityScale = 1f;

        private void Awake()
        {
            rb = GetComponent<Rigidbody>();
            spawnTime = Time.time;
        }

        private void Start()
        {
            // Destroy projectile after lifetime
            Destroy(gameObject, lifetime);
        }

        private void Update()
        {
            if (hasHit) return;

            // Update homing behavior
            if (isHoming && target != null && target.CurrentHealth > 0)
            {
                UpdateHoming();
            }

            // Check for manual collision (in case Rigidbody collision fails)
            CheckManualCollision();
        }

        private void FixedUpdate()
        {
            if (hasHit) return;

            // Apply custom gravity if needed
            if (useGravity && gravityScale != 1f)
            {
                rb.AddForce(Physics.gravity * (gravityScale - 1f), ForceMode.Acceleration);
            }
        }

        /// <summary>
        /// Initialize the projectile with target and weapon data
        /// </summary>
        public void Initialize(Unit attacker, Unit target, Vector3 targetPosition, WeaponData weaponData)
        {
            this.attacker = attacker;
            this.target = target;
            this.targetPosition = targetPosition;
            this.weaponData = weaponData;
            this.speed = weaponData.projectileSpeed;
            this.isHoming = weaponData.weaponType == WeaponType.Energy; // Energy weapons home

            // Calculate initial velocity
            CalculateTrajectory();

            // Set up visual effects
            SetupVisualEffects();
        }

        private void CalculateTrajectory()
        {
            Vector3 direction = (targetPosition - transform.position).normalized;
            
            if (useGravity && !isHoming)
            {
                // Calculate ballistic trajectory
                initialVelocity = CalculateBallisticVelocity(transform.position, targetPosition, speed);
                rb.linearVelocity = initialVelocity;
            }
            else
            {
                // Direct trajectory
                rb.linearVelocity = direction * speed;
            }

            // Orient projectile towards target
            if (rb.linearVelocity != Vector3.zero)
            {
                transform.rotation = Quaternion.LookRotation(rb.linearVelocity.normalized);
            }
        }

        private Vector3 CalculateBallisticVelocity(Vector3 start, Vector3 target, float speed)
        {
            Vector3 displacement = target - start;
            Vector3 horizontalDisplacement = new Vector3(displacement.x, 0, displacement.z);
            float horizontalDistance = horizontalDisplacement.magnitude;
            float verticalDistance = displacement.y;

            // Calculate time of flight for given speed
            float timeOfFlight = horizontalDistance / speed;

            // Calculate required vertical velocity to reach target
            float verticalVelocity = (verticalDistance / timeOfFlight) + (0.5f * Mathf.Abs(Physics.gravity.y) * timeOfFlight);

            Vector3 horizontalVelocity = horizontalDisplacement.normalized * speed;
            return horizontalVelocity + Vector3.up * verticalVelocity;
        }

        private void UpdateHoming()
        {
            if (target == null || target.CurrentHealth <= 0) return;

            Vector3 targetDirection = (target.transform.position - transform.position).normalized;
            Vector3 currentDirection = rb.linearVelocity.normalized;

            // Gradually turn towards target
            Vector3 newDirection = Vector3.Slerp(currentDirection, targetDirection, homingStrength * Time.deltaTime);
            rb.linearVelocity = newDirection * speed;

            // Update rotation
            transform.rotation = Quaternion.LookRotation(rb.linearVelocity.normalized);
        }

        private void CheckManualCollision()
        {
            // Cast a ray from previous position to current position
            Vector3 currentPosition = transform.position;
            Vector3 previousPosition = currentPosition - rb.linearVelocity * Time.deltaTime;
            
            float distance = Vector3.Distance(previousPosition, currentPosition);
            Vector3 direction = (currentPosition - previousPosition).normalized;

            if (Physics.Raycast(previousPosition, direction, out RaycastHit hit, distance))
            {
                OnHit(hit);
            }
        }

        private void OnTriggerEnter(Collider other)
        {
            if (hasHit) return;

            // Create a fake hit for trigger collision
            RaycastHit hit = new RaycastHit();
            // Note: This is a simplified approach. In a real implementation,
            // you'd want to calculate the proper hit point and normal.
            
            OnHit(hit, other);
        }

        private void OnCollisionEnter(Collision collision)
        {
            if (hasHit) return;

            RaycastHit hit = new RaycastHit();
            if (collision.contacts.Length > 0)
            {
                // Use the first contact point
                ContactPoint contact = collision.contacts[0];
                // Create a RaycastHit-like structure from collision data
                // This is a simplified approach
            }

            OnHit(hit, collision.collider);
        }

        private void OnHit(RaycastHit hit, Collider hitCollider = null)
        {
            if (hasHit) return;
            hasHit = true;

            Vector3 hitPoint = hitCollider != null ? hitCollider.ClosestPoint(transform.position) : hit.point;
            if (hitPoint == Vector3.zero) hitPoint = transform.position;

            // Check what we hit
            Unit hitUnit = (hitCollider ?? hit.collider)?.GetComponent<Unit>();

            if (weaponData.isExplosive)
            {
                // Create explosion
                if (CombatSystem.Instance != null)
                {
                    CombatSystem.Instance.CreateExplosion(hitPoint, weaponData.explosionRadius, weaponData.damage, attacker);
                }
            }
            else if (hitUnit != null)
            {
                // Direct hit on unit
                if (CombatSystem.Instance != null)
                {
                    CombatSystem.Instance.ApplyDamage(attacker, hitUnit, weaponData.damage, hitPoint);
                }
            }

            // Remove from combat system tracking
            if (CombatSystem.Instance != null)
            {
                CombatSystem.Instance.RemoveProjectile(this);
            }

            // Destroy projectile
            DestroyProjectile();
        }

        private void SetupVisualEffects()
        {
            // Set up trail renderer if present
            if (trailRenderer != null)
            {
                trailRenderer.time = 0.5f;
                trailRenderer.startWidth = 0.1f;
                trailRenderer.endWidth = 0.02f;
                
                // Set color based on weapon type
                Color trailColor = GetTrailColor();
                trailRenderer.startColor = trailColor;
                trailRenderer.endColor = new Color(trailColor.r, trailColor.g, trailColor.b, 0f);
            }

            // Set up projectile light if present
            if (projectileLight != null)
            {
                projectileLight.color = GetTrailColor();
                projectileLight.intensity = 2f;
                projectileLight.range = 5f;
            }

            // Set projectile color based on weapon type
            Renderer renderer = GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.color = GetTrailColor();
                
                // Make energy weapons glow
                if (weaponData.weaponType == WeaponType.Energy)
                {
                    renderer.material.EnableKeyword("_EMISSION");
                    renderer.material.SetColor("_EmissionColor", GetTrailColor() * 2f);
                }
            }
        }

        private Color GetTrailColor()
        {
            switch (weaponData.weaponType)
            {
                case WeaponType.Ballistic:
                    return Color.yellow;
                case WeaponType.Energy:
                    return Color.cyan;
                case WeaponType.Explosive:
                    return Color.red;
                default:
                    return Color.white;
            }
        }

        private void DestroyProjectile()
        {
            // Disable colliders to prevent multiple hits
            Collider[] colliders = GetComponents<Collider>();
            foreach (Collider col in colliders)
            {
                col.enabled = false;
            }

            // Stop movement
            if (rb != null)
            {
                rb.linearVelocity = Vector3.zero;
                rb.isKinematic = true;
            }

            // Fade out trail if present
            if (trailRenderer != null)
            {
                trailRenderer.time = 0.1f;
            }

            // Destroy after a short delay to allow effects to finish
            Destroy(gameObject, 0.5f);
        }

        /// <summary>
        /// Get the remaining lifetime of the projectile
        /// </summary>
        public float GetRemainingLifetime()
        {
            return lifetime - (Time.time - spawnTime);
        }

        /// <summary>
        /// Check if the projectile is still active
        /// </summary>
        public bool IsActive()
        {
            return !hasHit && GetRemainingLifetime() > 0;
        }

        /// <summary>
        /// Manually detonate the projectile (for explosive projectiles)
        /// </summary>
        public void Detonate()
        {
            if (hasHit) return;

            if (weaponData.isExplosive && CombatSystem.Instance != null)
            {
                CombatSystem.Instance.CreateExplosion(transform.position, weaponData.explosionRadius, weaponData.damage, attacker);
            }

            hasHit = true;
            DestroyProjectile();
        }
    }
}
