using UnityEngine;
#if ENABLE_INPUT_SYSTEM
using UnityEngine.InputSystem;
#endif

// ColdVor - Simple RTS camera controller for Unity 6
// NO MOVEMENT unless there is input. Camera stays exactly where you put it in editor.

[DisallowMultipleComponent]
[RequireComponent(typeof(Camera))]
public class RTSCameraController : MonoBehaviour
{
    [Header("Pan / Translation")]
    public float panSpeed = 20f;
    public KeyCode fastPanModifier = KeyCode.LeftShift;
    public float fastPanMultiplier = 2f;

    [Header("Edge Panning")]
    public bool edgePanning = true;
    public int edgeThickness = 12;

    [Header("Rotate (hold RMB)")]
    public float rotateSpeed = 30f;
    public float pitchSpeed = 30f;
    public float minPitch = 20f;
    public float maxPitch = 85f;
    public bool invertRotateX = false;
    public bool invertRotateY = false;

    [Header("Zoom (Scroll Wheel)")]
    public float zoomSpeed = 5f;

    [Header("Input Options")]
    public bool allowArrowKeys = true;
    public bool allowWASD = true;
    public bool allowMMBPan = true;

    [Header("Terrain Following")]
    public bool followTerrain = true;
    public float minHeightAboveTerrain = 60f;
    public LayerMask terrainLayerMask = 1;

    [Header("Camera Bounds")]
    public bool constrainToBounds = true;
    public Vector2 terrainSize = new Vector2(1000f, 1000f); // Will be auto-detected if terrain found
    public Vector2 terrainCenter = Vector2.zero; // Will be auto-detected if terrain found

    // Camera rotation state (controlled by RTSInputManager)
    private bool isCameraRotationActive = false;

    void Start()
    {
        // Subscribe to centralized input events
        if (ColdVor.RTS.RTSInputManager.Instance != null)
        {
            ColdVor.RTS.RTSInputManager.Instance.OnStartCameraRotation += StartCameraRotation;
            ColdVor.RTS.RTSInputManager.Instance.OnStopCameraRotation += StopCameraRotation;
        }

        // Auto-detect terrain bounds if available
        DetectTerrainBounds();
    }

    void OnDestroy()
    {
        // Unsubscribe from events
        if (ColdVor.RTS.RTSInputManager.Instance != null)
        {
            ColdVor.RTS.RTSInputManager.Instance.OnStartCameraRotation -= StartCameraRotation;
            ColdVor.RTS.RTSInputManager.Instance.OnStopCameraRotation -= StopCameraRotation;
        }
    }

    private void StartCameraRotation()
    {
        isCameraRotationActive = true;
    }

    private void StopCameraRotation()
    {
        isCameraRotationActive = false;
    }

    void Update()
    {
        float dt = Time.deltaTime;

        // Read all input
        Vector3 panInput = ReadPanInput(dt);
        Vector2 rotateInput = ReadRotateInput(dt);
        float zoomInput = ReadZoomInput();

        // Only do ANYTHING if there's actual input
        if (panInput.sqrMagnitude > 0.001f || rotateInput.sqrMagnitude > 0.001f || Mathf.Abs(zoomInput) > 0.001f)
        {
            // Apply pan movement
            if (panInput.sqrMagnitude > 0.001f)
            {
                transform.position += panInput;
            }

            // Apply rotation
            if (rotateInput.sqrMagnitude > 0.001f)
            {
                Vector3 euler = transform.rotation.eulerAngles;
                float yaw = euler.y + rotateInput.x;
                float pitch = euler.x + rotateInput.y;
                if (pitch > 180f) pitch -= 360f;
                pitch = Mathf.Clamp(pitch, minPitch, maxPitch);
                transform.rotation = Quaternion.Euler(pitch, yaw, 0f);
            }

            // Apply zoom with terrain height constraint
            if (Mathf.Abs(zoomInput) > 0.001f)
            {
                Vector3 forward = transform.forward;
                Vector3 proposedMovement = forward * zoomInput * zoomSpeed * dt;
                Vector3 proposedPosition = transform.position + proposedMovement;

                // Check if zoom would violate height constraint
                if (followTerrain)
                {
                    if (IsPositionTooLowForTerrain(proposedPosition))
                    {
                        // Don't apply zoom if it would make camera too low
                        return;
                    }
                }

                transform.position = proposedPosition;
            }

            // Apply terrain following and bounds constraints after all movement
            if (followTerrain || constrainToBounds)
            {
                ApplyTerrainConstraints();
            }
        }
    }

    Vector3 ReadPanInput(float dt)
    {
        Vector3 delta = Vector3.zero;

        // Keyboard input
        Vector2 input = Vector2.zero;
        if (allowWASD)
        {
            if (IsKeyHeld(KeyCode.W)) input.y += 1f;
            if (IsKeyHeld(KeyCode.S)) input.y -= 1f;
            if (IsKeyHeld(KeyCode.A)) input.x -= 1f;
            if (IsKeyHeld(KeyCode.D)) input.x += 1f;
        }
        if (allowArrowKeys)
        {
            if (IsKeyHeld(KeyCode.UpArrow)) input.y += 1f;
            if (IsKeyHeld(KeyCode.DownArrow)) input.y -= 1f;
            if (IsKeyHeld(KeyCode.LeftArrow)) input.x -= 1f;
            if (IsKeyHeld(KeyCode.RightArrow)) input.x += 1f;
        }

        // Edge panning
        if (edgePanning && !IsRMBHeld() && !IsMMBHeld())
        {
            Vector2 mousePos = GetMousePosition();
            if (mousePos.x <= edgeThickness) input.x -= 1f;
            if (mousePos.x >= Screen.width - edgeThickness) input.x += 1f;
            if (mousePos.y <= edgeThickness) input.y -= 1f;
            if (mousePos.y >= Screen.height - edgeThickness) input.y += 1f;
        }

        // MMB drag
        if (allowMMBPan && IsMMBHeld())
        {
            Vector2 mouseDelta = GetMouseDelta();
            input.x -= mouseDelta.x * 0.01f;
            input.y -= mouseDelta.y * 0.01f;
        }

        // Convert to world movement
        if (input.sqrMagnitude > 0.001f)
        {
            Vector3 right = transform.right; right.y = 0f; right.Normalize();
            Vector3 forward = transform.forward; forward.y = 0f; forward.Normalize();
            float speed = panSpeed * (IsKeyHeld(fastPanModifier) ? fastPanMultiplier : 1f);
            delta = (right * input.x + forward * input.y) * speed * dt;
        }

        return delta;
    }

    Vector2 ReadRotateInput(float dt)
    {
        // Only allow rotation when the centralized input manager says it's okay
        if (!isCameraRotationActive) return Vector2.zero;

        Vector2 mouseDelta = GetMouseDelta();
        if (mouseDelta.sqrMagnitude < 0.001f) return Vector2.zero;

        float yawDelta = (invertRotateX ? -1f : 1f) * mouseDelta.x * rotateSpeed * dt;
        float pitchDelta = (invertRotateY ? -1f : 1f) * -mouseDelta.y * pitchSpeed * dt;

        return new Vector2(yawDelta, pitchDelta);
    }

    float ReadZoomInput()
    {
        return GetScrollDelta().y;
    }





    // ---------- Input Abstraction (Old and New Systems) ----------
    bool IsKeyHeld(KeyCode key)
    {
#if ENABLE_INPUT_SYSTEM
        // Input System does not have KeyCode directly for all cases; map common ones
        // Use Keyboard.current fallback
        var kb = Keyboard.current;
        if (kb == null) return false;
        switch (key)
        {
            case KeyCode.W: return kb.wKey.isPressed;
            case KeyCode.A: return kb.aKey.isPressed;
            case KeyCode.S: return kb.sKey.isPressed;
            case KeyCode.D: return kb.dKey.isPressed;
            case KeyCode.LeftShift: return kb.leftShiftKey.isPressed;
            case KeyCode.RightShift: return kb.rightShiftKey.isPressed;
            case KeyCode.UpArrow: return kb.upArrowKey.isPressed;
            case KeyCode.DownArrow: return kb.downArrowKey.isPressed;
            case KeyCode.LeftArrow: return kb.leftArrowKey.isPressed;
            case KeyCode.RightArrow: return kb.rightArrowKey.isPressed;
            default:
                // Fallback to legacy Input for uncommon keys if available
                return Input.GetKey(key);
        }
#else
        return Input.GetKey(key);
#endif
    }

    bool IsRMBHeld()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null && Mouse.current.rightButton.isPressed;
#else
        return Input.GetMouseButton(1);
#endif
    }
    bool IsMMBHeld()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null && Mouse.current.middleButton.isPressed;
#else
        return Input.GetMouseButton(2);
#endif
    }

    Vector2 GetMouseDelta()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null ? Mouse.current.delta.ReadValue() : Vector2.zero;
#else
        return new Vector2(Input.GetAxisRaw("Mouse X"), Input.GetAxisRaw("Mouse Y"));
#endif
    }

    Vector2 GetScrollDelta()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null ? Mouse.current.scroll.ReadValue() : Vector2.zero;
#else
        return new Vector2(0f, Input.mouseScrollDelta.y);
#endif
    }

    Vector2 GetMousePosition()
    {
#if ENABLE_INPUT_SYSTEM
        return Mouse.current != null ? Mouse.current.position.ReadValue() : Vector2.negativeInfinity;
#else
        return Input.mousePosition;
#endif
    }

    void DetectTerrainBounds()
    {
        // Try to find terrain component
        Terrain terrain = FindFirstObjectByType<Terrain>();
        if (terrain != null)
        {
            TerrainData terrainData = terrain.terrainData;
            Vector3 terrainPos = terrain.transform.position;

            // Set terrain bounds based on actual terrain
            terrainSize = new Vector2(terrainData.size.x, terrainData.size.z);
            terrainCenter = new Vector2(terrainPos.x + terrainData.size.x * 0.5f, terrainPos.z + terrainData.size.z * 0.5f);

            Debug.Log($"Auto-detected terrain bounds: Size={terrainSize}, Center={terrainCenter}");
        }
        else
        {
            Debug.Log("No terrain found, using default bounds");
        }
    }

    void ApplyTerrainConstraints()
    {
        Vector3 currentPos = transform.position;
        Vector3 newPos = currentPos;
        bool positionChanged = false;

        // Apply bounds constraints
        if (constrainToBounds)
        {
            float halfSizeX = terrainSize.x * 0.5f;
            float halfSizeZ = terrainSize.y * 0.5f;

            float minX = terrainCenter.x - halfSizeX;
            float maxX = terrainCenter.x + halfSizeX;
            float minZ = terrainCenter.y - halfSizeZ;
            float maxZ = terrainCenter.y + halfSizeZ;

            if (newPos.x < minX) { newPos.x = minX; positionChanged = true; }
            if (newPos.x > maxX) { newPos.x = maxX; positionChanged = true; }
            if (newPos.z < minZ) { newPos.z = minZ; positionChanged = true; }
            if (newPos.z > maxZ) { newPos.z = maxZ; positionChanged = true; }
        }

        // Apply terrain height following
        if (followTerrain)
        {
            Vector3 rayStart = new Vector3(newPos.x, newPos.y + 100f, newPos.z);

            if (Physics.Raycast(rayStart, Vector3.down, out RaycastHit hit, 200f, terrainLayerMask))
            {
                float terrainHeight = hit.point.y;
                float requiredHeight = terrainHeight + minHeightAboveTerrain;

                // Only adjust if camera is below the required height
                if (newPos.y < requiredHeight)
                {
                    newPos.y = requiredHeight;
                    positionChanged = true;
                }
            }
        }

        // Apply position changes if any constraints were applied
        if (positionChanged)
        {
            transform.position = newPos;
        }
    }

    bool IsPositionTooLowForTerrain(Vector3 position)
    {
        Vector3 rayStart = new Vector3(position.x, position.y + 100f, position.z);

        if (Physics.Raycast(rayStart, Vector3.down, out RaycastHit hit, 200f, terrainLayerMask))
        {
            float terrainHeight = hit.point.y;
            float requiredHeight = terrainHeight + minHeightAboveTerrain;

            return position.y < requiredHeight;
        }

        return false; // If no terrain hit, allow the movement
    }
}

