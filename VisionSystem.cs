using System.Collections.Generic;
using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Manages line-of-sight, fog of war, and unit visibility systems
    /// Handles detection ranges, vision blocking, and intelligence gathering
    /// </summary>
    public class VisionSystem : MonoBehaviour
    {
        [Header("Fog of War")]
        [SerializeField] private bool enableFogOfWar = true;
        [SerializeField] private float fogUpdateInterval = 0.5f;
        [SerializeField] private LayerMask visionBlockingLayers = 1;
        [SerializeField] private int fogResolution = 256;

        [Header("Vision Settings")]
        [SerializeField] private float maxVisionRange = 50f;
        [SerializeField] private float visionFadeDistance = 5f;
        [SerializeField] private bool useHeightAdvantage = true;
        [SerializeField] private float heightAdvantageMultiplier = 1.5f;

        [Header("Stealth System")]
        [SerializeField] private bool enableStealth = true;
        [SerializeField] private float stealthDetectionRange = 3f;

        // Fog of war data
        private Texture2D fogTexture;
        private Color[] fogPixels;
        private Vector2 mapSize = new Vector2(100f, 100f);
        private Vector2 mapCenter = Vector2.zero;
        private float lastFogUpdate;

        // Vision tracking
        private Dictionary<Unit, VisionData> unitVisionData = new Dictionary<Unit, VisionData>();
        private List<Unit> playerUnits = new List<Unit>();
        private List<Unit> allUnits = new List<Unit>();

        // Singleton pattern
        private static VisionSystem instance;
        public static VisionSystem Instance => instance;

        // Events
        public System.Action<Unit, Unit> OnUnitSpotted;
        public System.Action<Unit, Unit> OnUnitLostSight;
        public System.Action<Vector2> OnFogOfWarUpdated;

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeFogOfWar();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            // Find all units and categorize them
            RefreshUnitLists();
            
            // Subscribe to unit events
            SubscribeToUnitEvents();
        }

        private void Update()
        {
            // Update fog of war periodically
            if (enableFogOfWar && Time.time - lastFogUpdate >= fogUpdateInterval)
            {
                UpdateFogOfWar();
                lastFogUpdate = Time.time;
            }

            // Update vision data for all units
            UpdateVisionData();
        }

        #region Fog of War

        private void InitializeFogOfWar()
        {
            if (!enableFogOfWar) return;

            // Create fog texture
            fogTexture = new Texture2D(fogResolution, fogResolution, TextureFormat.RGBA32, false);
            fogPixels = new Color[fogResolution * fogResolution];

            // Initialize with full fog (black)
            for (int i = 0; i < fogPixels.Length; i++)
            {
                fogPixels[i] = Color.black;
            }

            fogTexture.SetPixels(fogPixels);
            fogTexture.Apply();

            // Auto-detect map size if terrain exists
            Terrain terrain = FindFirstObjectByType<Terrain>();
            if (terrain != null)
            {
                mapSize = new Vector2(terrain.terrainData.size.x, terrain.terrainData.size.z);
                mapCenter = new Vector2(terrain.transform.position.x + mapSize.x / 2f, 
                                       terrain.transform.position.z + mapSize.y / 2f);
            }
        }

        private void UpdateFogOfWar()
        {
            if (!enableFogOfWar || fogTexture == null) return;

            // Reset fog to black
            for (int i = 0; i < fogPixels.Length; i++)
            {
                fogPixels[i] = Color.Lerp(fogPixels[i], Color.black, Time.deltaTime * 2f);
            }

            // Reveal areas around player units
            foreach (Unit unit in playerUnits)
            {
                if (unit != null && unit.gameObject.activeInHierarchy)
                {
                    RevealAreaAroundUnit(unit);
                }
            }

            // Apply changes to texture
            fogTexture.SetPixels(fogPixels);
            fogTexture.Apply();

            OnFogOfWarUpdated?.Invoke(mapCenter);
        }

        private void RevealAreaAroundUnit(Unit unit)
        {
            Vector3 worldPos = unit.transform.position;
            Vector2 mapPos = WorldToMapPosition(worldPos);
            
            float visionRange = GetUnitVisionRange(unit);
            int pixelRadius = Mathf.RoundToInt((visionRange / mapSize.x) * fogResolution);

            int centerX = Mathf.RoundToInt(mapPos.x * fogResolution);
            int centerY = Mathf.RoundToInt(mapPos.y * fogResolution);

            for (int x = -pixelRadius; x <= pixelRadius; x++)
            {
                for (int y = -pixelRadius; y <= pixelRadius; y++)
                {
                    int pixelX = centerX + x;
                    int pixelY = centerY + y;

                    if (pixelX >= 0 && pixelX < fogResolution && pixelY >= 0 && pixelY < fogResolution)
                    {
                        float distance = Mathf.Sqrt(x * x + y * y);
                        if (distance <= pixelRadius)
                        {
                            // Calculate visibility based on distance and line of sight
                            Vector3 targetWorldPos = MapToWorldPosition(new Vector2((float)pixelX / fogResolution, (float)pixelY / fogResolution));
                            
                            if (HasLineOfSight(worldPos, targetWorldPos))
                            {
                                float visibility = 1f - (distance / pixelRadius);
                                visibility = Mathf.Clamp01(visibility);

                                int pixelIndex = pixelY * fogResolution + pixelX;
                                fogPixels[pixelIndex] = Color.Lerp(fogPixels[pixelIndex], Color.white, visibility);
                            }
                        }
                    }
                }
            }
        }

        private Vector2 WorldToMapPosition(Vector3 worldPos)
        {
            float x = (worldPos.x - (mapCenter.x - mapSize.x / 2f)) / mapSize.x;
            float y = (worldPos.z - (mapCenter.y - mapSize.y / 2f)) / mapSize.y;
            return new Vector2(Mathf.Clamp01(x), Mathf.Clamp01(y));
        }

        private Vector3 MapToWorldPosition(Vector2 mapPos)
        {
            float x = mapCenter.x - mapSize.x / 2f + mapPos.x * mapSize.x;
            float z = mapCenter.y - mapSize.y / 2f + mapPos.y * mapSize.y;
            return new Vector3(x, 0, z);
        }

        #endregion

        #region Vision Detection

        private void UpdateVisionData()
        {
            foreach (Unit unit in allUnits)
            {
                if (unit == null || !unit.gameObject.activeInHierarchy) continue;

                UpdateUnitVision(unit);
            }
        }

        private void UpdateUnitVision(Unit unit)
        {
            if (!unitVisionData.ContainsKey(unit))
            {
                unitVisionData[unit] = new VisionData();
            }

            VisionData visionData = unitVisionData[unit];
            List<Unit> previouslyVisible = new List<Unit>(visionData.visibleUnits);
            visionData.visibleUnits.Clear();

            float visionRange = GetUnitVisionRange(unit);

            // Check all other units for visibility
            foreach (Unit otherUnit in allUnits)
            {
                if (otherUnit == null || otherUnit == unit || !otherUnit.gameObject.activeInHierarchy) continue;

                if (CanUnitSeeUnit(unit, otherUnit, visionRange))
                {
                    visionData.visibleUnits.Add(otherUnit);

                    // Trigger spotted event for new sightings
                    if (!previouslyVisible.Contains(otherUnit))
                    {
                        OnUnitSpotted?.Invoke(unit, otherUnit);
                    }
                }
            }

            // Trigger lost sight events
            foreach (Unit lostUnit in previouslyVisible)
            {
                if (!visionData.visibleUnits.Contains(lostUnit))
                {
                    OnUnitLostSight?.Invoke(unit, lostUnit);
                }
            }
        }

        private bool CanUnitSeeUnit(Unit observer, Unit target, float visionRange)
        {
            Vector3 observerPos = observer.transform.position + Vector3.up * 1.5f; // Eye level
            Vector3 targetPos = target.transform.position + Vector3.up * 1f; // Target center

            float distance = Vector3.Distance(observerPos, targetPos);

            // Check range
            if (distance > visionRange) return false;

            // Check if target is stealthed and within stealth detection range
            if (enableStealth && IsUnitStealthed(target))
            {
                if (distance > stealthDetectionRange) return false;
            }

            // Check field of view
            if (!IsInFieldOfView(observer, target)) return false;

            // Check line of sight
            if (!HasLineOfSight(observerPos, targetPos)) return false;

            // Check fog of war (if enabled and observer is player unit)
            if (enableFogOfWar && observer.Faction == Faction.Player)
            {
                if (!IsPositionRevealed(targetPos)) return false;
            }

            return true;
        }

        private float GetUnitVisionRange(Unit unit)
        {
            float baseRange = unit.DetectionRange;

            // Apply height advantage
            if (useHeightAdvantage)
            {
                float height = unit.transform.position.y;
                float averageHeight = 0f; // Could be calculated from terrain

                if (height > averageHeight + 5f)
                {
                    baseRange *= heightAdvantageMultiplier;
                }
            }

            return Mathf.Min(baseRange, maxVisionRange);
        }

        private float GetVisionFadeMultiplier(float distance, float maxRange)
        {
            // Apply vision fade at the edge of detection range
            float fadeStartDistance = maxRange - visionFadeDistance;
            if (distance > fadeStartDistance)
            {
                float fadeProgress = (distance - fadeStartDistance) / visionFadeDistance;
                return 1f - fadeProgress;
            }
            return 1f;
        }

        private bool IsInFieldOfView(Unit observer, Unit target)
        {
            Vector3 directionToTarget = (target.transform.position - observer.transform.position).normalized;
            float angle = Vector3.Angle(observer.transform.forward, directionToTarget);
            return angle <= observer.FieldOfViewAngle / 2f;
        }

        private bool HasLineOfSight(Vector3 from, Vector3 to)
        {
            Vector3 direction = (to - from).normalized;
            float distance = Vector3.Distance(from, to);

            // Cast ray to check for obstacles
            if (Physics.Raycast(from, direction, out RaycastHit hit, distance, visionBlockingLayers))
            {
                // Check if we hit the target or an obstacle
                Unit hitUnit = hit.collider.GetComponent<Unit>();
                if (hitUnit != null)
                {
                    // We hit a unit - check if it's our target
                    Vector3 targetPosition = to;
                    return Vector3.Distance(hit.point, targetPosition) < 1f;
                }
                
                // We hit an obstacle
                return false;
            }

            return true; // No obstacles found
        }

        private bool IsUnitStealthed(Unit unit)
        {
            // Check if unit has stealth component or ability
            // This is a placeholder - implement based on your stealth system
            return false;
        }

        private bool IsPositionRevealed(Vector3 worldPos)
        {
            if (!enableFogOfWar || fogTexture == null) return true;

            Vector2 mapPos = WorldToMapPosition(worldPos);
            int pixelX = Mathf.RoundToInt(mapPos.x * fogResolution);
            int pixelY = Mathf.RoundToInt(mapPos.y * fogResolution);

            if (pixelX >= 0 && pixelX < fogResolution && pixelY >= 0 && pixelY < fogResolution)
            {
                int pixelIndex = pixelY * fogResolution + pixelX;
                return fogPixels[pixelIndex].r > 0.1f; // Revealed if not completely black
            }

            return false;
        }

        #endregion

        #region Public Interface

        /// <summary>
        /// Get all units visible to a specific unit
        /// </summary>
        public List<Unit> GetVisibleUnits(Unit observer)
        {
            if (unitVisionData.ContainsKey(observer))
            {
                return new List<Unit>(unitVisionData[observer].visibleUnits);
            }
            return new List<Unit>();
        }

        /// <summary>
        /// Check if one unit can see another
        /// </summary>
        public bool CanSeeUnit(Unit observer, Unit target)
        {
            if (unitVisionData.ContainsKey(observer))
            {
                return unitVisionData[observer].visibleUnits.Contains(target);
            }
            return false;
        }

        /// <summary>
        /// Get the fog of war texture for UI display
        /// </summary>
        public Texture2D GetFogOfWarTexture()
        {
            return fogTexture;
        }

        /// <summary>
        /// Refresh the list of units (call when units are created/destroyed)
        /// </summary>
        public void RefreshUnitLists()
        {
            allUnits.Clear();
            playerUnits.Clear();

            Unit[] units = FindObjectsByType<Unit>(FindObjectsSortMode.None);
            foreach (Unit unit in units)
            {
                allUnits.Add(unit);
                if (unit.Faction == Faction.Player)
                {
                    playerUnits.Add(unit);
                }
            }
        }

        #endregion

        private void SubscribeToUnitEvents()
        {
            // Subscribe to unit creation/destruction events if available
            // This would be implemented based on your unit spawning system
        }
    }

    /// <summary>
    /// Data structure for tracking unit vision information
    /// </summary>
    [System.Serializable]
    public class VisionData
    {
        public List<Unit> visibleUnits = new List<Unit>();
        public float lastVisionUpdate;
        public Vector3 lastKnownPosition;
    }
}
