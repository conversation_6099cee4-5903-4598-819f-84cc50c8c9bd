using System.Collections.Generic;
using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Tactical awareness engine that processes intelligence and coordinates unit responses
    /// Handles threat assessment, tactical decision making, and coordinated unit behavior
    /// </summary>
    public class TacticalAwareness : MonoBehaviour
    {
        [Header("Awareness Settings")]
        [SerializeField] private float awarenessUpdateInterval = 1f;
        [SerializeField] private float situationAssessmentRange = 30f;
        [SerializeField] private bool enablePredictiveAnalysis = true;
        [SerializeField] private float predictionTimeHorizon = 5f;

        [Header("Decision Making")]
        [SerializeField] private float decisionConfidenceThreshold = 0.7f;
        [SerializeField] private bool enableGroupDecisions = true;
        [SerializeField] private float groupDecisionRadius = 25f;
        [SerializeField] private int maxConcurrentDecisions = 5;

        [Header("Tactical Responses")]
        [SerializeField] private bool enableAutomaticResponses = true;
        [SerializeField] private float responseDelay = 0.5f;
        [SerializeField] private float coordinationBonus = 0.2f;

        // Awareness data
        private Dictionary<Unit, TacticalSituation> unitSituations = new Dictionary<Unit, TacticalSituation>();
        private Dictionary<Faction, FactionAwareness> factionAwareness = new Dictionary<Faction, FactionAwareness>();
        private List<TacticalDecision> activeDecisions = new List<TacticalDecision>();

        // Update tracking
        private float lastAwarenessUpdate;
        private List<Unit> allUnits = new List<Unit>();

        // Singleton pattern
        private static TacticalAwareness instance;
        public static TacticalAwareness Instance => instance;

        // Events
        public System.Action<Unit, TacticalSituation> OnSituationAssessed;
        public System.Action<TacticalDecision> OnTacticalDecisionMade;
        public System.Action<Unit, TacticalResponse> OnTacticalResponseExecuted;

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeAwareness();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            RefreshUnitList();
            SubscribeToEvents();
        }

        private void Update()
        {
            if (Time.time - lastAwarenessUpdate >= awarenessUpdateInterval)
            {
                UpdateTacticalAwareness();
                lastAwarenessUpdate = Time.time;
            }

            ProcessTacticalDecisions();
        }

        #region Awareness System

        private void InitializeAwareness()
        {
            foreach (Faction faction in System.Enum.GetValues(typeof(Faction)))
            {
                factionAwareness[faction] = new FactionAwareness
                {
                    faction = faction,
                    overallThreatLevel = ThreatLevel.None,
                    knownEnemies = new List<Unit>(),
                    tacticalAdvantage = 0f
                };
            }
        }

        private void UpdateTacticalAwareness()
        {
            RefreshUnitList();

            // Update individual unit situations
            foreach (Unit unit in allUnits)
            {
                if (unit == null || !unit.gameObject.activeInHierarchy) continue;
                UpdateUnitSituation(unit);
            }

            // Update faction-level awareness
            foreach (Faction faction in System.Enum.GetValues(typeof(Faction)))
            {
                UpdateFactionAwareness(faction);
            }

            // Generate tactical decisions
            if (enableAutomaticResponses)
            {
                GenerateTacticalDecisions();
            }
        }

        private void UpdateUnitSituation(Unit unit)
        {
            if (!unitSituations.ContainsKey(unit))
            {
                unitSituations[unit] = new TacticalSituation { unit = unit };
            }

            TacticalSituation situation = unitSituations[unit];
            
            // Assess immediate threats
            AssessImmediateThreats(unit, situation);
            
            // Assess tactical position
            AssessTacticalPosition(unit, situation);
            
            // Assess support availability
            AssessSupportAvailability(unit, situation);
            
            // Calculate overall situation assessment
            CalculateSituationScore(situation);

            // Predict future developments if enabled
            if (enablePredictiveAnalysis)
            {
                PredictFutureSituation(unit, situation);
            }

            OnSituationAssessed?.Invoke(unit, situation);
        }

        private void AssessImmediateThreats(Unit unit, TacticalSituation situation)
        {
            situation.immediateThreats.Clear();
            situation.threatLevel = ThreatLevel.None;

            float highestThreat = 0f;

            foreach (Unit enemy in unit.DetectedEnemies)
            {
                if (enemy == null) continue;

                float distance = Vector3.Distance(unit.transform.position, enemy.transform.position);
                float threatScore = CalculateThreatScore(unit, enemy, distance);

                if (threatScore > 0.1f)
                {
                    situation.immediateThreats.Add(new ThreatInfo
                    {
                        threatUnit = enemy,
                        threatScore = threatScore,
                        distance = distance,
                        lastSeen = Time.time
                    });

                    highestThreat = Mathf.Max(highestThreat, threatScore);
                }
            }

            // Determine threat level
            if (highestThreat >= 0.8f) situation.threatLevel = ThreatLevel.Critical;
            else if (highestThreat >= 0.6f) situation.threatLevel = ThreatLevel.High;
            else if (highestThreat >= 0.4f) situation.threatLevel = ThreatLevel.Medium;
            else if (highestThreat >= 0.2f) situation.threatLevel = ThreatLevel.Low;
            else situation.threatLevel = ThreatLevel.None;
        }

        private void AssessTacticalPosition(Unit unit, TacticalSituation situation)
        {
            // Assess cover, elevation, flanking opportunities, etc.
            situation.hasGoodPosition = EvaluatePosition(unit);
            situation.canRetreat = CanRetreatSafely(unit);
            situation.flankingOpportunities = FindFlankingOpportunities(unit);
        }

        private void AssessSupportAvailability(Unit unit, TacticalSituation situation)
        {
            situation.nearbyAllies.Clear();
            situation.availableSupport = 0f;

            foreach (Unit ally in unit.DetectedFriendlies)
            {
                if (ally == null) continue;

                float distance = Vector3.Distance(unit.transform.position, ally.transform.position);
                if (distance <= situationAssessmentRange)
                {
                    situation.nearbyAllies.Add(ally);
                    situation.availableSupport += CalculateSupportValue(ally, distance);
                }
            }
        }

        private void CalculateSituationScore(TacticalSituation situation)
        {
            float score = 0.5f; // Neutral starting point

            // Factor in threat level
            switch (situation.threatLevel)
            {
                case ThreatLevel.Critical: score -= 0.4f; break;
                case ThreatLevel.High: score -= 0.3f; break;
                case ThreatLevel.Medium: score -= 0.2f; break;
                case ThreatLevel.Low: score -= 0.1f; break;
            }

            // Factor in position quality
            if (situation.hasGoodPosition) score += 0.2f;
            if (situation.canRetreat) score += 0.1f;

            // Factor in support
            score += Mathf.Min(situation.availableSupport * 0.1f, 0.3f);

            // Factor in flanking opportunities
            score += situation.flankingOpportunities.Count * 0.05f;

            situation.overallScore = Mathf.Clamp01(score);
        }

        private void PredictFutureSituation(Unit unit, TacticalSituation situation)
        {
            // Simple prediction based on current movement and threat trajectories
            Vector3 predictedPosition = unit.transform.position + unit.NavAgent.velocity * predictionTimeHorizon;
            
            // Predict threat changes
            foreach (var threat in situation.immediateThreats)
            {
                if (threat.threatUnit != null && threat.threatUnit.NavAgent != null)
                {
                    Vector3 threatPredictedPos = threat.threatUnit.transform.position + 
                                               threat.threatUnit.NavAgent.velocity * predictionTimeHorizon;
                    
                    float predictedDistance = Vector3.Distance(predictedPosition, threatPredictedPos);
                    threat.predictedThreatScore = CalculateThreatScore(unit, threat.threatUnit, predictedDistance);
                }
            }
        }

        #endregion

        #region Decision Making

        private void GenerateTacticalDecisions()
        {
            foreach (Unit unit in allUnits)
            {
                if (unit == null || !unit.gameObject.activeInHierarchy) continue;
                if (unit.Faction == Faction.Player) continue; // Don't make decisions for player units

                if (unitSituations.ContainsKey(unit))
                {
                    TacticalSituation situation = unitSituations[unit];
                    GenerateUnitDecisions(unit, situation);
                }
            }
        }

        private void GenerateUnitDecisions(Unit unit, TacticalSituation situation)
        {
            // Don't make new decisions if unit is already executing one
            if (HasActiveDecision(unit)) return;

            TacticalDecision decision = null;

            // Decision making based on situation
            switch (situation.threatLevel)
            {
                case ThreatLevel.Critical:
                    decision = DecideOnCriticalThreat(unit, situation);
                    break;
                case ThreatLevel.High:
                    decision = DecideOnHighThreat(unit, situation);
                    break;
                case ThreatLevel.Medium:
                    decision = DecideOnModerateThreat(unit, situation);
                    break;
                case ThreatLevel.Low:
                    decision = DecideOnLowThreat(unit, situation);
                    break;
                case ThreatLevel.None:
                    decision = DecideOnNoThreat(unit, situation);
                    break;
            }

            if (decision != null && decision.confidence >= decisionConfidenceThreshold)
            {
                activeDecisions.Add(decision);
                ExecuteTacticalDecision(decision);
                OnTacticalDecisionMade?.Invoke(decision);
            }
        }

        private TacticalDecision DecideOnCriticalThreat(Unit unit, TacticalSituation situation)
        {
            // Critical threat - retreat or call for immediate support
            if (situation.canRetreat && situation.availableSupport < 0.5f)
            {
                return new TacticalDecision
                {
                    unit = unit,
                    decisionType = TacticalDecisionType.Retreat,
                    confidence = 0.9f,
                    priority = 1f,
                    timestamp = Time.time
                };
            }
            else if (situation.availableSupport >= 0.5f)
            {
                return new TacticalDecision
                {
                    unit = unit,
                    decisionType = TacticalDecisionType.CallSupport,
                    confidence = 0.8f,
                    priority = 0.9f,
                    timestamp = Time.time
                };
            }

            return null;
        }

        private TacticalDecision DecideOnHighThreat(Unit unit, TacticalSituation situation)
        {
            // High threat - engage with support or reposition
            if (situation.availableSupport >= 0.3f)
            {
                return new TacticalDecision
                {
                    unit = unit,
                    decisionType = TacticalDecisionType.EngageWithSupport,
                    confidence = 0.7f,
                    priority = 0.8f,
                    timestamp = Time.time
                };
            }
            else if (!situation.hasGoodPosition)
            {
                return new TacticalDecision
                {
                    unit = unit,
                    decisionType = TacticalDecisionType.Reposition,
                    confidence = 0.6f,
                    priority = 0.7f,
                    timestamp = Time.time
                };
            }

            return null;
        }

        private TacticalDecision DecideOnModerateThreat(Unit unit, TacticalSituation situation)
        {
            // Moderate threat - engage or use flanking
            if (situation.flankingOpportunities.Count > 0)
            {
                return new TacticalDecision
                {
                    unit = unit,
                    decisionType = TacticalDecisionType.Flank,
                    confidence = 0.6f,
                    priority = 0.6f,
                    timestamp = Time.time,
                    targetPosition = situation.flankingOpportunities[0]
                };
            }
            else
            {
                return new TacticalDecision
                {
                    unit = unit,
                    decisionType = TacticalDecisionType.Engage,
                    confidence = 0.5f,
                    priority = 0.5f,
                    timestamp = Time.time
                };
            }
        }

        private TacticalDecision DecideOnLowThreat(Unit unit, TacticalSituation situation)
        {
            // Low threat - advance or investigate
            return new TacticalDecision
            {
                unit = unit,
                decisionType = TacticalDecisionType.Advance,
                confidence = 0.4f,
                priority = 0.3f,
                timestamp = Time.time
            };
        }

        private TacticalDecision DecideOnNoThreat(Unit unit, TacticalSituation situation)
        {
            // No immediate threat - patrol or support others
            if (situation.nearbyAllies.Count > 0)
            {
                // Check if any allies need support
                foreach (Unit ally in situation.nearbyAllies)
                {
                    if (unitSituations.ContainsKey(ally) && 
                        unitSituations[ally].threatLevel >= ThreatLevel.Medium)
                    {
                        return new TacticalDecision
                        {
                            unit = unit,
                            decisionType = TacticalDecisionType.SupportAlly,
                            confidence = 0.6f,
                            priority = 0.4f,
                            timestamp = Time.time,
                            targetUnit = ally
                        };
                    }
                }
            }

            return null; // Continue current behavior
        }

        private void ExecuteTacticalDecision(TacticalDecision decision)
        {
            EnemyAI ai = decision.unit.GetComponent<EnemyAI>();
            if (ai == null) return;

            switch (decision.decisionType)
            {
                case TacticalDecisionType.Retreat:
                    // AI will handle retreat in its state machine
                    break;
                    
                case TacticalDecisionType.Engage:
                    Unit target = decision.unit.GetClosestEnemy();
                    if (target != null)
                    {
                        ai.EngageTarget(target);
                    }
                    break;
                    
                case TacticalDecisionType.Flank:
                    if (decision.targetPosition != Vector3.zero)
                    {
                        ai.InvestigatePosition(decision.targetPosition);
                    }
                    break;
                    
                case TacticalDecisionType.Reposition:
                    Vector3 betterPosition = FindBetterPosition(decision.unit);
                    ai.InvestigatePosition(betterPosition);
                    break;
                    
                case TacticalDecisionType.SupportAlly:
                    if (decision.targetUnit != null)
                    {
                        ai.InvestigatePosition(decision.targetUnit.transform.position);
                    }
                    break;
            }

            OnTacticalResponseExecuted?.Invoke(decision.unit, new TacticalResponse
            {
                responseType = decision.decisionType,
                timestamp = Time.time
            });
        }

        #endregion

        #region Helper Methods

        private float CalculateThreatScore(Unit observer, Unit threat, float distance)
        {
            if (threat == null) return 0f;

            float baseScore = 1f;
            
            // Distance factor
            float maxThreatRange = observer.DetectionRange;
            float distanceFactor = 1f - (distance / maxThreatRange);
            
            // Unit type factor
            float typeFactor = GetUnitTypeThreatMultiplier(threat.UnitType, observer.UnitType);
            
            // Health factor
            float healthFactor = (float)threat.CurrentHealth / threat.MaxHealth;
            
            return baseScore * distanceFactor * typeFactor * healthFactor;
        }

        private float GetUnitTypeThreatMultiplier(UnitType threatType, UnitType observerType)
        {
            // Simple threat matrix
            if (threatType == UnitType.Tank && observerType == UnitType.Infantry) return 2f;
            if (threatType == UnitType.Artillery) return 1.5f;
            if (threatType == UnitType.Aircraft) return 1.8f;
            return 1f;
        }

        private bool EvaluatePosition(Unit unit)
        {
            // Simple position evaluation - could be enhanced with cover analysis
            return Random.value > 0.5f; // Placeholder
        }

        private bool CanRetreatSafely(Unit unit)
        {
            // Check if unit can retreat without being flanked
            return true; // Placeholder
        }

        private List<Vector3> FindFlankingOpportunities(Unit unit)
        {
            List<Vector3> opportunities = new List<Vector3>();
            
            foreach (Unit enemy in unit.DetectedEnemies)
            {
                if (enemy == null) continue;
                
                // Calculate potential flanking positions
                Vector3 enemyPos = enemy.transform.position;
                Vector3 flankLeft = enemyPos + enemy.transform.right * -10f;
                Vector3 flankRight = enemyPos + enemy.transform.right * 10f;
                
                opportunities.Add(flankLeft);
                opportunities.Add(flankRight);
            }
            
            return opportunities;
        }

        private float CalculateSupportValue(Unit ally, float distance)
        {
            float baseValue = 1f;
            float distanceFactor = 1f - (distance / situationAssessmentRange);
            
            // Factor in ally's combat capability
            float combatFactor = ally.CanAttack ? 1f : 0.5f;
            
            return baseValue * distanceFactor * combatFactor;
        }

        private Vector3 FindBetterPosition(Unit unit)
        {
            // Find a tactically superior position
            Vector3 currentPos = unit.transform.position;
            Vector3 betterPos = currentPos + Random.insideUnitSphere * 15f;
            betterPos.y = currentPos.y;
            
            return betterPos;
        }

        private bool HasActiveDecision(Unit unit)
        {
            return activeDecisions.Exists(d => d.unit == unit && Time.time - d.timestamp < 5f);
        }

        private void ProcessTacticalDecisions()
        {
            // Clean up old decisions
            activeDecisions.RemoveAll(d => Time.time - d.timestamp > 10f);
        }

        private void UpdateFactionAwareness(Faction faction)
        {
            if (!factionAwareness.ContainsKey(faction)) return;

            FactionAwareness awareness = factionAwareness[faction];
            
            // Update based on unit situations
            List<Unit> factionUnits = GetFactionUnits(faction);
            float totalThreat = 0f;
            
            foreach (Unit unit in factionUnits)
            {
                if (unitSituations.ContainsKey(unit))
                {
                    TacticalSituation situation = unitSituations[unit];
                    totalThreat += (float)situation.threatLevel / 4f; // Normalize threat level
                }
            }
            
            if (factionUnits.Count > 0)
            {
                totalThreat /= factionUnits.Count;
            }
            
            // Update overall threat level
            awareness.overallThreatLevel = (ThreatLevel)Mathf.RoundToInt(totalThreat * 4f);
        }

        private List<Unit> GetFactionUnits(Faction faction)
        {
            List<Unit> units = new List<Unit>();
            foreach (Unit unit in allUnits)
            {
                if (unit != null && unit.Faction == faction)
                {
                    units.Add(unit);
                }
            }
            return units;
        }

        private void RefreshUnitList()
        {
            allUnits.Clear();
            Unit[] units = FindObjectsByType<Unit>(FindObjectsSortMode.None);
            allUnits.AddRange(units);
        }

        private void SubscribeToEvents()
        {
            // Subscribe to relevant events from other systems
            if (IntelligenceNetwork.Instance != null)
            {
                IntelligenceNetwork.Instance.OnIntelReportCreated += OnIntelReportReceived;
            }
        }

        private void OnIntelReportReceived(IntelReport report)
        {
            // Process intelligence reports to update awareness
            // This could trigger immediate tactical responses
        }

        #endregion

        #region Public Interface

        /// <summary>
        /// Get the tactical situation for a specific unit
        /// </summary>
        public TacticalSituation GetUnitSituation(Unit unit)
        {
            return unitSituations.ContainsKey(unit) ? unitSituations[unit] : null;
        }

        /// <summary>
        /// Get faction-level awareness
        /// </summary>
        public FactionAwareness GetFactionAwareness(Faction faction)
        {
            return factionAwareness.ContainsKey(faction) ? factionAwareness[faction] : null;
        }

        /// <summary>
        /// Force a tactical assessment for a unit
        /// </summary>
        public void ForceAssessment(Unit unit)
        {
            if (unit != null)
            {
                UpdateUnitSituation(unit);
            }
        }

        #endregion
    }

    /// <summary>
    /// Tactical situation assessment for a unit
    /// </summary>
    [System.Serializable]
    public class TacticalSituation
    {
        public Unit unit;
        public ThreatLevel threatLevel;
        public List<ThreatInfo> immediateThreats = new List<ThreatInfo>();
        public List<Unit> nearbyAllies = new List<Unit>();
        public bool hasGoodPosition;
        public bool canRetreat;
        public float availableSupport;
        public List<Vector3> flankingOpportunities = new List<Vector3>();
        public float overallScore;
        public float lastUpdate;
    }

    /// <summary>
    /// Information about a specific threat
    /// </summary>
    [System.Serializable]
    public class ThreatInfo
    {
        public Unit threatUnit;
        public float threatScore;
        public float distance;
        public float lastSeen;
        public float predictedThreatScore;
    }

    /// <summary>
    /// Faction-level tactical awareness
    /// </summary>
    [System.Serializable]
    public class FactionAwareness
    {
        public Faction faction;
        public ThreatLevel overallThreatLevel;
        public List<Unit> knownEnemies = new List<Unit>();
        public float tacticalAdvantage;
        public float lastUpdate;
    }

    /// <summary>
    /// Tactical decision made by the AI
    /// </summary>
    [System.Serializable]
    public class TacticalDecision
    {
        public Unit unit;
        public TacticalDecisionType decisionType;
        public float confidence;
        public float priority;
        public float timestamp;
        public Vector3 targetPosition;
        public Unit targetUnit;
    }

    /// <summary>
    /// Tactical response executed by a unit
    /// </summary>
    [System.Serializable]
    public class TacticalResponse
    {
        public TacticalDecisionType responseType;
        public float timestamp;
        public bool successful;
    }

    /// <summary>
    /// Types of tactical decisions
    /// </summary>
    public enum TacticalDecisionType
    {
        Engage,             // Attack enemy
        Retreat,            // Withdraw from combat
        Flank,              // Attempt flanking maneuver
        Reposition,         // Move to better position
        CallSupport,        // Request reinforcements
        EngageWithSupport,  // Attack with allied support
        SupportAlly,        // Assist friendly unit
        Advance,            // Move forward
        Defend,             // Hold position
        Investigate         // Check area of interest
    }
