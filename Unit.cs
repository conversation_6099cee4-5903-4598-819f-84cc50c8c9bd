using UnityEngine;
using UnityEngine.AI;
using System.Collections.Generic;

namespace ColdVor.RTS
{
    /// <summary>
    /// Base class for all selectable units in the RTS system
    /// Implements ISelectable and provides common unit functionality
    /// </summary>
    [RequireComponent(typeof(Collider))]
    public class Unit : MonoBehaviour, ISelectable
    {
        [Header("Unit Info")]
        [SerializeField] private string unitName = "Unit";
        [SerializeField] private UnitType unitType = UnitType.Infantry;
        [SerializeField] private Faction faction = Faction.Player;
        [SerializeField] private int maxHealth = 100;
        [SerializeField] private float moveSpeed = 5f;
        private float originalNavSpeed; // Store original speed

        [Header("Combat")]
        [SerializeField] private float attackRange = 8f;
        [SerializeField] private int attackDamage = 25;
        [SerializeField] private float attackCooldown = 2f;
        [SerializeField] private float projectileSpeed = 20f;
        [SerializeField] private bool canAttack = true;
        [SerializeField] private WeaponData weaponData;

        [Header("Detection")]
        [SerializeField] private float detectionRange = 15f;
        [SerializeField] private float fieldOfViewAngle = 120f;
        [SerializeField] private LayerMask detectionLayerMask = -1;
        [SerializeField] private bool useLineOfSight = true;


        [Header("Selection")]
        [SerializeField] private GameObject selectionRing;
        [SerializeField] private bool canBeSelected = true;

        [Header("Movement")]
        [SerializeField] private bool canMove = true;
        [SerializeField] private float stoppingDistance = 0.1f;
        [SerializeField] private float rotationSpeed = .1f;
        [SerializeField] private float minTurnAngleForSlowdown = 15f; // Start slowing at this angle
        [SerializeField] private float maxTurnAngleForStop = 45f; // Stop completely at this angle
        [SerializeField] private float turnSpeedMultiplier = 0.2f; // Speed when turning

        [Header("Terrain Alignment")]
        [SerializeField] private bool alignToTerrain = true;
        [SerializeField] private float alignmentSpeed = 10f;
        [SerializeField] private float maxTiltAngle = 45f; // Maximum tilt before clamping
        [SerializeField] private LayerMask groundLayerMask = 1;

        // Private fields
        private bool isSelected = false;
        private int currentHealth;
        private NavMeshAgent navAgent;
        private Collider unitCollider;
        private Quaternion targetYRotation;

        private Renderer[] renderers;
        private Transform visualMesh; // Separate visual representation

        // Combat fields
        private float lastAttackTime;
        private Unit currentTarget;
        private bool isInCombat = false;

        // Detection fields
        private List<Unit> detectedEnemies = new List<Unit>();
        private List<Unit> detectedFriendlies = new List<Unit>();
        private float lastDetectionUpdate;

        // Properties implementing ISelectable
        public bool IsSelected => isSelected;
        public GameObject GameObject => gameObject;
        public Transform Transform => transform;

        // Unit properties
        public string UnitName => unitName;
        public UnitType UnitType => unitType;
        public Faction Faction => faction;
        public int CurrentHealth => currentHealth;
        public int MaxHealth => maxHealth;
        public float MoveSpeed => moveSpeed;
        public bool CanMove => canMove && navAgent != null;
        public NavMeshAgent NavAgent => navAgent;

        // Combat properties
        public float AttackRange => attackRange;
        public int AttackDamage => attackDamage;
        public float AttackCooldown => attackCooldown;
        public bool CanAttack => canAttack;
        public Unit CurrentTarget => currentTarget;
        public bool IsInCombat => isInCombat;
        public WeaponData WeaponData => weaponData;

        // Detection properties
        public float DetectionRange => detectionRange;
        public float FieldOfViewAngle => fieldOfViewAngle;
        public List<Unit> DetectedEnemies => new List<Unit>(detectedEnemies);
        public List<Unit> DetectedFriendlies => new List<Unit>(detectedFriendlies);

        // Events
        public System.Action<Unit> OnUnitSelected;
        public System.Action<Unit> OnUnitDeselected;
        public System.Action<Unit, int> OnHealthChanged;
        public System.Action<Unit> OnUnitDestroyed;

        // Combat events
        public System.Action<Unit, Unit> OnAttackStarted;
        public System.Action<Unit, Unit, int> OnDamageDealt;
        public System.Action<Unit, Unit> OnTargetAcquired;
        public System.Action<Unit> OnTargetLost;

        // Detection events
        public System.Action<Unit, Unit> OnEnemyDetected;
        public System.Action<Unit, Unit> OnEnemyLost;
        public System.Action<Unit, Unit> OnFriendlyDetected;

        protected virtual void Awake()
        {
            navAgent = GetComponent<NavMeshAgent>();
            unitCollider = GetComponent<Collider>();
            renderers = GetComponentsInChildren<Renderer>();

            currentHealth = maxHealth;

            // Initialize faction-based settings
            InitializeFactionSettings();

            if (navAgent != null)
            {
                navAgent.speed = moveSpeed;
                originalNavSpeed = moveSpeed; // Store original speed
                navAgent.stoppingDistance = Mathf.Max(0.5f, stoppingDistance);
                navAgent.angularSpeed = rotationSpeed;
                navAgent.acceleration = 8f;
                navAgent.autoBraking = true;
                navAgent.radius = 1f;
                navAgent.obstacleAvoidanceType = ObstacleAvoidanceType.HighQualityObstacleAvoidance;
                navAgent.updateRotation = false; // Let us handle rotation

                // Let NavMeshAgent handle its own rotation - we'll handle visual separately
            }

             // Initialize target rotation
            targetYRotation = transform.rotation;
            // Setup visual mesh separation
            SetupVisualMesh();

            if (selectionRing == null)
            {
                CreateSelectionRing();
            }

            if (selectionRing != null)
            {
                selectionRing.SetActive(false);
            }
        }

        protected virtual void Start()
        {
        }

        protected virtual void Update()
        {
            HandleMovement();
            UpdateDetection();
            UpdateCombat();

            if (alignToTerrain)
            {
                AlignToTerrain();
            }

            // Ensure NavMeshAgent stays properly positioned relative to visual mesh
            SynchronizeNavMeshAgent();
        }


        // Add this new method:
private void HandleMovement()
{
   if (navAgent == null || !navAgent.hasPath) 
    {
        // Reset speed when not moving
        if (navAgent != null) navAgent.speed = originalNavSpeed;
        return;
    }

    // Calculate desired rotation based on movement direction
    Vector3 desiredVelocity = navAgent.desiredVelocity;
    if (desiredVelocity.magnitude > 0.1f)
    {
        // Calculate target rotation based on movement direction
        Vector3 lookDirection = desiredVelocity.normalized;
        lookDirection.y = 0; // Keep only horizontal rotation
        
        if (lookDirection != Vector3.zero)
        {
            Quaternion desiredRotation = Quaternion.LookRotation(lookDirection);
            targetYRotation = desiredRotation;
        }
    }

    // Calculate how much we need to turn
    float yawDifference = Quaternion.Angle(transform.rotation, targetYRotation);
    
    // Adjust movement speed based on turn angle
    float speedMultiplier = 1f;
    
    if (yawDifference > minTurnAngleForSlowdown)
    {
        if (yawDifference >= maxTurnAngleForStop)
        {
            // Stop completely for sharp turns
            speedMultiplier = 0f;
        }
        else
        {
            // Gradually slow down as turn angle increases
            float turnProgress = (yawDifference - minTurnAngleForSlowdown) / (maxTurnAngleForStop - minTurnAngleForSlowdown);
            speedMultiplier = Mathf.Lerp(1f, turnSpeedMultiplier, turnProgress);
        }
    }
    
    // Apply speed adjustment to NavMeshAgent
    navAgent.speed = originalNavSpeed * speedMultiplier;

    // Smoothly rotate towards target
    if (yawDifference > 1f) // Only rotate if there's a meaningful difference
    {
        transform.rotation = Quaternion.RotateTowards(
            transform.rotation, 
            targetYRotation, 
            rotationSpeed * Time.deltaTime
        );
    }
}

        private void SynchronizeNavMeshAgent()
        {
            if (navAgent == null || visualMesh == null) return;

            // Keep the NavMeshAgent's position synchronized with the main transform
            // while allowing the visual mesh to be offset for terrain alignment
            Vector3 agentPosition = transform.position;

            // Ensure the agent stays on the NavMesh
            if (UnityEngine.AI.NavMesh.SamplePosition(agentPosition, out UnityEngine.AI.NavMeshHit hit, 2f, UnityEngine.AI.NavMesh.AllAreas))
            {
                // Only update if the difference is significant to avoid jittering
                float distance = Vector3.Distance(navAgent.transform.position, hit.position);
                if (distance > 0.1f)
                {
                    navAgent.Warp(hit.position);
                }
            }
        }

        #region ISelectable Implementation

        public virtual void OnSelected()
        {
            if (!canBeSelected) return;

            isSelected = true;

            if (selectionRing != null)
            {
                selectionRing.SetActive(true);
            }

            OnUnitSelected?.Invoke(this);
        }

        public virtual void OnDeselected()
        {
            isSelected = false;

            if (selectionRing != null)
            {
                selectionRing.SetActive(false);
            }

            OnUnitDeselected?.Invoke(this);
        }

        public virtual Bounds GetSelectionBounds()
        {
            if (unitCollider == null)
                unitCollider = GetComponent<Collider>();

            if (unitCollider != null)
            {
                return unitCollider.bounds;
            }

            if (renderers == null || renderers.Length == 0)
                renderers = GetComponentsInChildren<Renderer>();

            Bounds bounds = new Bounds(transform.position, Vector3.one);
            if (renderers != null)
            {
                foreach (var renderer in renderers)
                {
                    if (renderer != null)
                    {
                        bounds.Encapsulate(renderer.bounds);
                    }
                }
            }

            return bounds;
        }

        public virtual bool CanBeSelected()
        {
            if (!canBeSelected || !gameObject.activeInHierarchy)
                return false;

            // Check if faction can be selected by player
            if (FactionManager.Instance != null)
            {
                return FactionManager.Instance.CanFactionBeSelectedByPlayer(faction);
            }

            // Default to allowing selection if no faction manager
            return true;
        }

        #endregion

        #region Movement Methods

        public virtual bool MoveTo(Vector3 destination)
        {
            if (!CanMove) return false;

            navAgent.SetDestination(destination);
            return true;
        }

        public virtual bool IsMoving()
        {
            if (navAgent == null) return false;

            // More lenient movement detection
            return navAgent.hasPath &&
                   navAgent.remainingDistance > navAgent.stoppingDistance + 0.1f &&
                   navAgent.velocity.magnitude > 0.1f;
        }

        public virtual void StopMovement()
        {
            if (navAgent != null && navAgent.enabled)
            {
                navAgent.ResetPath();
            }
        }

        #endregion

        #region Health and Combat

        public virtual void TakeDamage(int damage, Unit attacker = null)
        {
            if (damage <= 0) return;

            // Check if damage should be applied based on faction relationships
            if (attacker != null && FactionManager.Instance != null)
            {
                if (!FactionManager.Instance.ShouldApplyDamage(attacker.Faction, this.Faction))
                {
                    return; // Don't apply damage due to faction relationships
                }
            }

            currentHealth = Mathf.Max(0, currentHealth - damage);
            OnHealthChanged?.Invoke(this, currentHealth);

            // Trigger damage dealt event for attacker
            if (attacker != null)
            {
                attacker.OnDamageDealt?.Invoke(attacker, this, damage);
            }

            if (currentHealth <= 0)
            {
                Die();
            }
        }

        public virtual void Heal(int amount)
        {
            if (amount <= 0) return;

            currentHealth = Mathf.Min(maxHealth, currentHealth + amount);
            OnHealthChanged?.Invoke(this, currentHealth);
        }

        protected virtual void Die()
        {
            OnUnitDestroyed?.Invoke(this);

            // Remove from selection if selected
            if (isSelected)
            {
                var inputManager = RTSInputManager.Instance;
                if (inputManager != null)
                {
                    inputManager.RemoveFromSelection(this);
                }
            }

            // Destroy the unit
            Destroy(gameObject);
        }

        #endregion

        #region Selection Ring

        private void CreateSelectionRing()
        {
            CreateMeshOutline();
        }

        private void CreateMeshOutline()
        {
            // Create outline container that follows visual mesh
            GameObject outlineContainer = new GameObject("SelectionOutline");
            outlineContainer.transform.SetParent(visualMesh != null ? visualMesh : transform);
            outlineContainer.transform.localPosition = Vector3.zero;
            outlineContainer.transform.localRotation = Quaternion.identity;
            outlineContainer.transform.localScale = Vector3.one;

            // Create outline at collider center height
            CreateColliderCenterOutline(outlineContainer.transform);

            selectionRing = outlineContainer;
        }

        private void CreateColliderCenterOutline(Transform parent)
        {
            if (unitCollider == null) return;

            GameObject outlineObj = new GameObject("ColliderOutline");
            outlineObj.transform.SetParent(parent);
            outlineObj.transform.localPosition = Vector3.zero;
            outlineObj.transform.localRotation = Quaternion.identity;

            LineRenderer lineRenderer = outlineObj.AddComponent<LineRenderer>();

            // Setup line renderer with selection ring material
            Material selectionMaterial = Resources.Load<Material>("Materials/SelectionRing");
            if (selectionMaterial == null)
            {
                selectionMaterial = new Material(Shader.Find("Unlit/Color"));
                selectionMaterial.color = new Color(0.4f, 0.7f, 0.4f, 0.8f); // Muted green
            }
            lineRenderer.material = selectionMaterial;

            lineRenderer.startWidth = 0.15f;
            lineRenderer.endWidth = 0.15f;
            lineRenderer.useWorldSpace = false;
            lineRenderer.loop = true;
            lineRenderer.numCapVertices = 5;
            lineRenderer.numCornerVertices = 5;
            lineRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
            lineRenderer.receiveShadows = false;

            // Create outline at collider center height (between min and max)
            Bounds bounds = unitCollider.bounds;
            Vector3 center = bounds.center;
            Vector3 size = bounds.size;

            // Use collider center Y (halfway between min and max)
            float centerY = center.y - transform.position.y; // Convert to local space

            Vector3[] outlinePoints = new Vector3[]
            {
                new Vector3(-size.x * 0.5f, centerY, -size.z * 0.5f), // Front-left
                new Vector3(size.x * 0.5f, centerY, -size.z * 0.5f),  // Front-right
                new Vector3(size.x * 0.5f, centerY, size.z * 0.5f),   // Back-right
                new Vector3(-size.x * 0.5f, centerY, size.z * 0.5f),  // Back-left
            };

            lineRenderer.positionCount = outlinePoints.Length;
            lineRenderer.SetPositions(outlinePoints);
        }







        #endregion

        #region Visual Mesh Setup

        private void SetupVisualMesh()
        {
            // Find or create visual mesh container
            visualMesh = transform.Find("VisualMesh");
            if (visualMesh == null)
            {
                // Create visual mesh container
                GameObject visualContainer = new GameObject("VisualMesh");
                visualContainer.transform.SetParent(transform);
                visualContainer.transform.localPosition = Vector3.zero;
                visualContainer.transform.localRotation = Quaternion.identity;
                visualContainer.transform.localScale = Vector3.one;

                // Move entire mesh hierarchies to preserve relationships
                Transform[] directChildren = new Transform[transform.childCount];
                for (int i = 0; i < transform.childCount; i++)
                {
                    directChildren[i] = transform.GetChild(i);
                }

                // Move all direct children that have renderers (preserving hierarchy)
                foreach (Transform child in directChildren)
                {
                    if (child.name != "VisualMesh" && HasRendererInHierarchy(child))
                    {

                        // Check if this child has a SkinnedMeshRenderer
                        SkinnedMeshRenderer skinnedRenderer = child.GetComponent<SkinnedMeshRenderer>();
                        if (skinnedRenderer != null)
                        {
                            HandleSkinnedMeshRenderer(skinnedRenderer, visualContainer.transform);
                        }
                        else
                        {
                            child.SetParent(visualContainer.transform);
                        }
                    }
                    else if (child.name != "VisualMesh")
                    {
                        Debug.Log($"NOT moving component (no renderer): {child.name}");
                    }
                }

                visualMesh = visualContainer.transform;
            }
        }

        private bool HasRendererInHierarchy(Transform obj)
        {
            // Check if this object or any of its children have renderers
            return obj.GetComponentsInChildren<Renderer>().Length > 0;
        }

        private bool IsChildOf(Transform child, Transform parent)
        {
            Transform current = child.parent;
            while (current != null)
            {
                if (current == parent)
                    return true;
                current = current.parent;
            }
            return false;
        }

        private void HandleSkinnedMeshRenderer(SkinnedMeshRenderer skinnedRenderer, Transform newParent)
        {
            Debug.Log($"Handling SkinnedMeshRenderer: {skinnedRenderer.name}");

            // Store original bones
            Transform[] originalBones = skinnedRenderer.bones;

            // Find the root bone (the one that's not a child of any other bone in the array)
            Transform rootBone = skinnedRenderer.rootBone;
            if (rootBone == null && originalBones.Length > 0)
            {
                // If no explicit root bone, find the common ancestor
                rootBone = FindCommonAncestor(originalBones);
            }

            // Move the skinned mesh renderer
            skinnedRenderer.transform.SetParent(newParent);

            // Move the root bone hierarchy to maintain the bone relationships
            if (rootBone != null && rootBone.parent != newParent)
            {
                rootBone.SetParent(newParent);
            }

            Debug.Log($"SkinnedMeshRenderer {skinnedRenderer.name} moved successfully with {originalBones.Length} bones");
        }

        private Transform FindCommonAncestor(Transform[] bones)
        {
            if (bones.Length == 0) return null;
            if (bones.Length == 1) return bones[0];

            // Start with the first bone and walk up the hierarchy
            Transform candidate = bones[0];
            while (candidate != null)
            {
                bool isCommonAncestor = true;
                for (int i = 1; i < bones.Length; i++)
                {
                    if (!IsChildOf(bones[i], candidate) && bones[i] != candidate)
                    {
                        isCommonAncestor = false;
                        break;
                    }
                }

                if (isCommonAncestor)
                    return candidate;

                candidate = candidate.parent;
            }

            return null; // No common ancestor found
        }

  

private Vector3 smoothedGroundPoint = Vector3.zero; // Add this as a class field
        private Vector3 lastValidGroundPoint = Vector3.zero; // Backup ground point
        private float lastGroundCheckTime = 0f; // Timing for ground checks

        private void AlignToTerrain()
        {
            if (visualMesh == null || unitCollider == null) return;

            // Higher frequency ground checks for smoother alignment
            if (Time.time - lastGroundCheckTime < 0.01f) return; // 100 FPS for ground checks
            lastGroundCheckTime = Time.time;

            // Multiple raycast points for more robust ground detection
            Vector3 unitCenter = transform.position;
            Vector3 unitForward = transform.forward;
            Vector3 unitRight = transform.right;

            // Cast from multiple points around the unit's base
            List<RaycastHit> groundHits = new List<RaycastHit>();
            Vector3[] raycastPoints = new Vector3[]
            {
                unitCenter, // Center
                unitCenter + unitForward * 0.5f, // Front
                unitCenter - unitForward * 0.5f, // Back
                unitCenter + unitRight * 0.5f, // Right
                unitCenter - unitRight * 0.5f  // Left
            };

            Vector3 averageGroundPoint = Vector3.zero;
            Vector3 averageNormal = Vector3.zero;
            int validHits = 0;

            foreach (Vector3 rayPoint in raycastPoints)
            {
                Vector3 rayStart = rayPoint + Vector3.up * 2f; // Start above unit
                if (Physics.Raycast(rayStart, Vector3.down, out RaycastHit hit, 10f, groundLayerMask))
                {
                    // Validate hit is reasonable (not too far from expected position)
                    float heightDifference = Mathf.Abs(hit.point.y - unitCenter.y);
                    if (heightDifference < 5f) // Reasonable height difference
                    {
                        groundHits.Add(hit);
                        averageGroundPoint += hit.point;
                        averageNormal += hit.normal;
                        validHits++;
                    }
                }
            }

            // Only proceed if we have valid ground hits
            if (validHits == 0)
            {
                // Use last valid ground point as fallback
                if (lastValidGroundPoint != Vector3.zero)
                {
                    averageGroundPoint = lastValidGroundPoint;
                    averageNormal = Vector3.up;
                    validHits = 1;
                }
                else
                {
                    return; // No valid ground found, skip this frame
                }
            }
            else
            {
                averageGroundPoint /= validHits;
                averageNormal = (averageNormal / validHits).normalized;
                lastValidGroundPoint = averageGroundPoint; // Store as backup
            }

            // Smooth the ground point over time to eliminate jittering
            if (smoothedGroundPoint == Vector3.zero)
            {
                smoothedGroundPoint = averageGroundPoint;
            }
            else
            {
                // Improved smoothing - faster response when going uphill, slower when going downhill
                float heightDifference = averageGroundPoint.y - smoothedGroundPoint.y;

                // If unit is not moving and height difference is small, don't adjust to prevent floating
                bool isMoving = navAgent != null && navAgent.velocity.magnitude > 0.1f;
                if (!isMoving && Mathf.Abs(heightDifference) < 0.05f)
                {
                    return; // Don't adjust position when stationary and close to ground
                }

                float smoothingSpeed;
                if (heightDifference > 0.1f)
                {
                    smoothingSpeed = 20f; // Instant uphill to prevent clipping
                }
                else if (heightDifference < -0.1f)
                {
                    smoothingSpeed = 15f; // Silky smooth downhill
                }
                else
                {
                    smoothingSpeed = 10f; // Smooth normal response
                }

                // Silky smooth exponential interpolation
                float dampingFactor = 1f - Mathf.Exp(-smoothingSpeed * Time.deltaTime);
                smoothedGroundPoint = Vector3.Lerp(smoothedGroundPoint, averageGroundPoint, dampingFactor);
            }

            // Ensure normal points up and isn't too steep
            if (averageNormal.y < 0.3f) averageNormal = Vector3.up;

            // Limit tilt angle
            float tiltAngle = Vector3.Angle(Vector3.up, averageNormal);
            if (tiltAngle > maxTiltAngle)
            {
                float t = maxTiltAngle / tiltAngle;
                averageNormal = Vector3.Slerp(Vector3.up, averageNormal, t).normalized;
            }

            // Create rotation from normal and main transform's forward
            Vector3 forward = transform.forward;
            Vector3 right = Vector3.Cross(averageNormal, forward).normalized;
            Vector3 adjustedForward = Vector3.Cross(right, averageNormal).normalized;

            Quaternion terrainRotation = Quaternion.LookRotation(adjustedForward, averageNormal);

            // Apply terrain alignment to visual mesh with proper slope following
            // Use the main transform's Y rotation combined with terrain tilt
            Quaternion mainYRotation = Quaternion.Euler(0, transform.eulerAngles.y, 0);
            Quaternion slopeRotation = Quaternion.FromToRotation(Vector3.up, averageNormal);
            Quaternion finalRotation = slopeRotation * mainYRotation;

            // Silky smooth rotation with instant alignment capability
            float rotationDamping = 1f - Mathf.Exp(-alignmentSpeed * Time.deltaTime * 6f);
            visualMesh.rotation = Quaternion.Slerp(visualMesh.rotation, finalRotation, rotationDamping);

            // Position the unit properly on the ground with more conservative positioning
            Bounds visualBounds = GetVisualBounds();
            float bottomOffset = visualBounds.min.y - visualBounds.center.y + 2.75f; // Reduced offset

            float targetCenterY = smoothedGroundPoint.y - bottomOffset;
            float localYOffset = targetCenterY - transform.position.y;

            // Clamp the Y offset to prevent extreme movements
            localYOffset = Mathf.Clamp(localYOffset, -2f, 2f);

            Vector3 targetPos = new Vector3(0, localYOffset, 0);

            // Ultra-smooth position interpolation using exponential damping
            float positionDamping = 1f - Mathf.Exp(-alignmentSpeed * Time.deltaTime * 6f);
            visualMesh.localPosition = Vector3.Lerp(visualMesh.localPosition, targetPos, positionDamping);
        }



        private Bounds GetVisualBounds()
        {
            if (visualMesh == null) return new Bounds();

            Renderer[] renderers = visualMesh.GetComponentsInChildren<Renderer>();
            if (renderers.Length == 0) return new Bounds();

            Bounds bounds = renderers[0].bounds;
            for (int i = 1; i < renderers.Length; i++)
            {
                bounds.Encapsulate(renderers[i].bounds);
            }

            return bounds;
        }



        #endregion

        #region Utility Methods

        public virtual float GetDistanceTo(Vector3 position)
        {
            return Vector3.Distance(transform.position, position);
        }

        public virtual float GetDistanceTo(Unit otherUnit)
        {
            return GetDistanceTo(otherUnit.transform.position);
        }

        public virtual Vector3 GetGroundPosition()
        {
            Vector3 position = transform.position;
            if (Physics.Raycast(position + Vector3.up * 10f, Vector3.down, out RaycastHit hit, 20f))
            {
                return hit.point;
            }
            return position;
        }

        #endregion

        #region Faction and Combat Methods

        /// <summary>
        /// Initialize faction-based settings from FactionManager
        /// </summary>
        private void InitializeFactionSettings()
        {
            if (FactionManager.Instance != null)
            {
                var factionData = FactionManager.Instance.GetFactionData(faction);
                if (factionData != null)
                {
                    // Override detection range if not manually set
                    if (detectionRange == 15f) // Default value
                    {
                        detectionRange = factionData.detectionRange;
                    }
                }
            }

            // Initialize weapon data if not set
            if (weaponData == null)
            {
                weaponData = new WeaponData
                {
                    weaponName = "Basic Weapon",
                    weaponType = WeaponType.Ballistic,
                    damage = attackDamage,
                    range = attackRange,
                    fireRate = 1f / attackCooldown,
                    projectileSpeed = projectileSpeed,
                    accuracy = 0.95f,
                    isExplosive = false,
                    explosionRadius = 0f
                };
            }
        }

        /// <summary>
        /// Update detection of nearby units
        /// </summary>
        private void UpdateDetection()
        {
            // Limit detection updates for performance
            if (Time.time - lastDetectionUpdate < 0.2f) return;
            lastDetectionUpdate = Time.time;

            if (FactionManager.Instance == null) return;

            // Clear previous detections
            var previousEnemies = new List<Unit>(detectedEnemies);
            var previousFriendlies = new List<Unit>(detectedFriendlies);
            detectedEnemies.Clear();
            detectedFriendlies.Clear();

            // Find all units in detection range
            Collider[] nearbyColliders = Physics.OverlapSphere(transform.position, detectionRange, detectionLayerMask);

            foreach (Collider col in nearbyColliders)
            {
                Unit otherUnit = col.GetComponent<Unit>();
                if (otherUnit == null || otherUnit == this) continue;

                // Check if unit is within field of view
                if (!IsInFieldOfView(otherUnit)) continue;

                // Check line of sight if enabled
                if (useLineOfSight && !HasLineOfSight(otherUnit)) continue;

                // Categorize based on faction relationship
                var relationship = FactionManager.Instance.GetFactionRelationship(this.faction, otherUnit.faction);

                if (relationship == FactionRelationship.Hostile)
                {
                    detectedEnemies.Add(otherUnit);

                    // Trigger event for newly detected enemies
                    if (!previousEnemies.Contains(otherUnit))
                    {
                        OnEnemyDetected?.Invoke(this, otherUnit);
                    }
                }
                else if (relationship == FactionRelationship.Friendly)
                {
                    detectedFriendlies.Add(otherUnit);

                    // Trigger event for newly detected friendlies
                    if (!previousFriendlies.Contains(otherUnit))
                    {
                        OnFriendlyDetected?.Invoke(this, otherUnit);
                    }
                }
            }

            // Trigger events for lost enemies
            foreach (Unit lostEnemy in previousEnemies)
            {
                if (!detectedEnemies.Contains(lostEnemy))
                {
                    OnEnemyLost?.Invoke(this, lostEnemy);
                }
            }
        }

        /// <summary>
        /// Check if a unit is within field of view
        /// </summary>
        private bool IsInFieldOfView(Unit target)
        {
            Vector3 directionToTarget = (target.transform.position - transform.position).normalized;
            float angle = Vector3.Angle(transform.forward, directionToTarget);
            return angle <= fieldOfViewAngle / 2f;
        }

        /// <summary>
        /// Check if there's a clear line of sight to target
        /// </summary>
        private bool HasLineOfSight(Unit target)
        {
            Vector3 rayStart = transform.position + Vector3.up * 1.5f; // Eye level
            Vector3 rayDirection = (target.transform.position + Vector3.up * 1.5f - rayStart).normalized;
            float distance = Vector3.Distance(rayStart, target.transform.position + Vector3.up * 1.5f);

            // Cast ray to target
            if (Physics.Raycast(rayStart, rayDirection, out RaycastHit hit, distance))
            {
                // Check if we hit the target or something else
                Unit hitUnit = hit.collider.GetComponent<Unit>();
                return hitUnit == target;
            }

            return true; // No obstacles found
        }

        /// <summary>
        /// Update combat behavior
        /// </summary>
        private void UpdateCombat()
        {
            if (!canAttack) return;

            // Check if current target is still valid
            if (currentTarget != null)
            {
                if (!IsValidTarget(currentTarget))
                {
                    SetTarget(null);
                }
                else
                {
                    // Try to attack current target
                    TryAttackTarget(currentTarget);
                }
            }
        }

        /// <summary>
        /// Check if a target is valid for combat
        /// </summary>
        private bool IsValidTarget(Unit target)
        {
            if (target == null || target.CurrentHealth <= 0) return false;

            float distance = Vector3.Distance(transform.position, target.transform.position);
            if (distance > attackRange) return false;

            // Check faction relationship
            if (FactionManager.Instance != null)
            {
                return FactionManager.Instance.AreFactionsHostile(this.faction, target.faction);
            }

            return false;
        }

        /// <summary>
        /// Set a new combat target
        /// </summary>
        public virtual void SetTarget(Unit newTarget)
        {
            if (currentTarget == newTarget) return;

            Unit previousTarget = currentTarget;
            currentTarget = newTarget;

            if (previousTarget != null)
            {
                OnTargetLost?.Invoke(this);
            }

            if (currentTarget != null)
            {
                OnTargetAcquired?.Invoke(this, currentTarget);
            }

            isInCombat = currentTarget != null;
        }

        /// <summary>
        /// Attempt to attack the specified target
        /// </summary>
        private void TryAttackTarget(Unit target)
        {
            if (Time.time - lastAttackTime < attackCooldown) return;
            if (!IsValidTarget(target)) return;

            lastAttackTime = Time.time;
            PerformAttack(target);
        }

        /// <summary>
        /// Perform an attack on the target
        /// </summary>
        protected virtual void PerformAttack(Unit target)
        {
            OnAttackStarted?.Invoke(this, target);

            // Use combat system for proper weapon handling
            if (CombatSystem.Instance != null && weaponData != null)
            {
                if (weaponData.weaponType == WeaponType.Ballistic || weaponData.weaponType == WeaponType.Explosive)
                {
                    // Fire projectile
                    CombatSystem.Instance.FireProjectile(this, target, weaponData);
                }
                else
                {
                    // Instant hit (energy weapons, etc.)
                    CombatSystem.Instance.FireInstantHit(this, target, weaponData);
                }
            }
            else
            {
                // Fallback to simple damage
                target.TakeDamage(attackDamage, this);
            }
        }

        /// <summary>
        /// Get the closest enemy unit within detection range
        /// </summary>
        public Unit GetClosestEnemy()
        {
            Unit closestEnemy = null;
            float closestDistance = float.MaxValue;

            foreach (Unit enemy in detectedEnemies)
            {
                float distance = Vector3.Distance(transform.position, enemy.transform.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closestEnemy = enemy;
                }
            }

            return closestEnemy;
        }

        /// <summary>
        /// Attack a specific target (public method for AI/player commands)
        /// </summary>
        public virtual bool AttackTarget(Unit target)
        {
            if (!canAttack || target == null) return false;

            if (FactionManager.Instance != null &&
                !FactionManager.Instance.AreFactionsHostile(this.faction, target.faction))
            {
                return false; // Can't attack non-hostile units
            }

            SetTarget(target);
            return true;
        }

        #endregion


    }

    /// <summary>
    /// Enum defining different types of units
    /// </summary>
    public enum UnitType
    {
        Infantry,
        LightVehicle,
        HeavyVehicle,
        Tank,
        Artillery,
        Aircraft,
        Building
    }

    /// <summary>
    /// Enum defining different factions in the game
    /// </summary>
    public enum Faction
    {
        Player,
        Enemy,
        Neutral,
        Ally
    }

    /// <summary>
    /// Enum defining relationships between factions
    /// </summary>
    public enum FactionRelationship
    {
        Friendly,
        Neutral,
        Hostile
    }
}
