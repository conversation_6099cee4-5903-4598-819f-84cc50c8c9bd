using System.Collections.Generic;
using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// AI Controller for enemy units with state machine behavior
    /// Handles autonomous decision making, patrol routes, and combat engagement
    /// </summary>
    [RequireComponent(typeof(Unit))]
    public class EnemyAI : MonoBehaviour
    {
        [Header("AI Behavior")]
        [SerializeField] private AIState initialState = AIState.Patrol;
        [SerializeField] private float stateUpdateInterval = 0.5f;
        [SerializeField] private float decisionMakingRange = 20f;

        [Header("Patrol Settings")]
        [SerializeField] private Transform[] patrolPoints;
        [SerializeField] private float patrolWaitTime = 2f;
        [SerializeField] private float patrolRadius = 10f;
        [SerializeField] private bool randomPatrol = false;

        [Header("Combat Settings")]
        [SerializeField] private float engagementRange = 12f;
        [SerializeField] private float retreatHealthThreshold = 0.3f;
        [SerializeField] private float retreatDistance = 15f;
        [SerializeField] private float pursuitRange = 25f;

        [Header("Group Behavior")]
        [SerializeField] private bool useGroupBehavior = true;
        [SerializeField] private float groupCommunicationRange = 20f;
        [SerializeField] private float reinforcementCallRange = 30f;

        // State machine
        private AIState currentState;
        private AIState previousState;
        private float lastStateUpdate;
        private float stateEnterTime;

        // References
        public Unit unit;
        private Vector3 homePosition;
        private Vector3 lastKnownEnemyPosition;

        // Patrol state
        private int currentPatrolIndex = 0;
        private float patrolWaitStartTime;
        private Vector3 randomPatrolCenter;

        // Combat state
        private Unit primaryTarget;
        private List<Unit> knownEnemies = new List<Unit>();
        private float lastEnemyContactTime;

        // Group coordination
        private List<EnemyAI> nearbyAllies = new List<EnemyAI>();
        private bool hasCalledForReinforcements = false;

        // Events
        public System.Action<EnemyAI, AIState, AIState> OnStateChanged;
        public System.Action<EnemyAI, Unit> OnEnemyEngaged;
        public System.Action<EnemyAI> OnRetreatStarted;

        // Properties
        public AIState CurrentState => currentState;
        public Unit PrimaryTarget => primaryTarget;
        public Vector3 HomePosition => homePosition;

        private void Awake()
        {
            unit = GetComponent<Unit>();
            homePosition = transform.position;
            randomPatrolCenter = homePosition;
            
            // Subscribe to unit events
            unit.OnEnemyDetected += OnEnemyDetected;
            unit.OnEnemyLost += OnEnemyLost;
            unit.OnHealthChanged += OnHealthChanged;
        }

        private void Start()
        {
            ChangeState(initialState);
            
            // Set up patrol points if none provided
            if (patrolPoints == null || patrolPoints.Length == 0)
            {
                SetupDefaultPatrolPoints();
            }
        }

        private void Update()
        {
            // Update state machine
            if (Time.time - lastStateUpdate >= stateUpdateInterval)
            {
                UpdateStateMachine();
                lastStateUpdate = Time.time;
            }

            // Update current state behavior
            UpdateCurrentState();
            
            // Update group coordination
            if (useGroupBehavior)
            {
                UpdateGroupCoordination();
            }
        }

        #region State Machine

        private void UpdateStateMachine()
        {
            AIState newState = DetermineNextState();
            
            if (newState != currentState)
            {
                ChangeState(newState);
            }
        }

        private AIState DetermineNextState()
        {
            // Health-based decisions
            float healthPercentage = (float)unit.CurrentHealth / unit.MaxHealth;
            if (healthPercentage <= retreatHealthThreshold && currentState != AIState.Retreat)
            {
                return AIState.Retreat;
            }

            // Enemy-based decisions within decision making range
            Unit closestEnemy = GetClosestEnemyInRange(decisionMakingRange);
            
            if (closestEnemy != null)
            {
                float distanceToEnemy = Vector3.Distance(transform.position, closestEnemy.transform.position);
                
                // Engage if enemy is close enough
                if (distanceToEnemy <= engagementRange)
                {
                    return AIState.Engage;
                }
                
                // Investigate if we lost contact recently
                if (currentState == AIState.Engage && distanceToEnemy <= pursuitRange)
                {
                    return AIState.Investigate;
                }
            }

            // Return to patrol if no threats and not retreating
            if (currentState == AIState.Retreat && healthPercentage > retreatHealthThreshold + 0.1f)
            {
                return AIState.Patrol;
            }

            if (currentState == AIState.Investigate && Time.time - lastEnemyContactTime > 10f)
            {
                return AIState.Patrol;
            }

            // Default behavior
            if (currentState == AIState.Idle)
            {
                return AIState.Patrol;
            }

            return currentState;
        }

        private void ChangeState(AIState newState)
        {
            if (newState == currentState) return;

            // Exit current state
            ExitState(currentState);
            
            previousState = currentState;
            currentState = newState;
            stateEnterTime = Time.time;
            
            // Enter new state
            EnterState(newState);
            
            OnStateChanged?.Invoke(this, previousState, currentState);
        }

        private void EnterState(AIState state)
        {
            switch (state)
            {
                case AIState.Idle:
                    unit.StopMovement();
                    break;
                    
                case AIState.Patrol:
                    hasCalledForReinforcements = false;
                    if (patrolPoints != null && patrolPoints.Length > 0)
                    {
                        MoveToNextPatrolPoint();
                    }
                    else
                    {
                        StartRandomPatrol();
                    }
                    break;
                    
                case AIState.Investigate:
                    if (lastKnownEnemyPosition != Vector3.zero)
                    {
                        unit.MoveTo(lastKnownEnemyPosition);
                    }
                    break;
                    
                case AIState.Engage:
                    Unit target = unit.GetClosestEnemy();
                    if (target != null)
                    {
                        SetPrimaryTarget(target);
                        OnEnemyEngaged?.Invoke(this, target);
                        
                        // Call for reinforcements if enabled
                        if (useGroupBehavior && !hasCalledForReinforcements)
                        {
                            CallForReinforcements();
                        }
                    }
                    break;
                    
                case AIState.Retreat:
                    OnRetreatStarted?.Invoke(this);
                    Vector3 retreatPosition = CalculateRetreatPosition();
                    unit.MoveTo(retreatPosition);
                    break;
            }
        }

        private void ExitState(AIState state)
        {
            switch (state)
            {
                case AIState.Engage:
                    SetPrimaryTarget(null);
                    break;
            }
        }

        #endregion

        #region State Behaviors

        private void UpdateCurrentState()
        {
            switch (currentState)
            {
                case AIState.Idle:
                    UpdateIdleState();
                    break;
                case AIState.Patrol:
                    UpdatePatrolState();
                    break;
                case AIState.Investigate:
                    UpdateInvestigateState();
                    break;
                case AIState.Engage:
                    UpdateEngageState();
                    break;
                case AIState.Retreat:
                    UpdateRetreatState();
                    break;
            }
        }

        private void UpdateIdleState()
        {
            // Just wait - state machine will handle transitions
        }

        private void UpdatePatrolState()
        {
            if (!unit.IsMoving())
            {
                // Check if we're waiting at a patrol point
                if (Time.time - patrolWaitStartTime >= patrolWaitTime)
                {
                    if (patrolPoints != null && patrolPoints.Length > 0)
                    {
                        MoveToNextPatrolPoint();
                    }
                    else
                    {
                        StartRandomPatrol();
                    }
                }
                else if (patrolWaitStartTime == 0)
                {
                    // Just arrived at patrol point
                    patrolWaitStartTime = Time.time;
                }
            }
        }

        private void UpdateInvestigateState()
        {
            if (!unit.IsMoving())
            {
                // Reached investigation point, look around briefly then return to patrol
                if (Time.time - stateEnterTime > 3f)
                {
                    ChangeState(AIState.Patrol);
                }
            }
        }

        private void UpdateEngageState()
        {
            if (primaryTarget != null && primaryTarget.CurrentHealth > 0)
            {
                float distanceToTarget = Vector3.Distance(transform.position, primaryTarget.transform.position);
                
                if (distanceToTarget <= unit.AttackRange)
                {
                    // In attack range - stop and attack
                    unit.StopMovement();
                    unit.AttackTarget(primaryTarget);
                }
                else if (distanceToTarget <= pursuitRange)
                {
                    // Pursue target
                    unit.MoveTo(primaryTarget.transform.position);
                }
                else
                {
                    // Target too far, investigate last known position
                    lastKnownEnemyPosition = primaryTarget.transform.position;
                    ChangeState(AIState.Investigate);
                }
            }
            else
            {
                // Target lost or dead
                SetPrimaryTarget(null);
                ChangeState(AIState.Investigate);
            }
        }

        private void UpdateRetreatState()
        {
            if (!unit.IsMoving())
            {
                // Reached retreat position, stay in retreat until health improves
                float healthPercentage = (float)unit.CurrentHealth / unit.MaxHealth;
                if (healthPercentage > retreatHealthThreshold + 0.2f)
                {
                    ChangeState(AIState.Patrol);
                }
            }
        }

        #endregion

        #region Helper Methods

        private void SetupDefaultPatrolPoints()
        {
            // Create a simple patrol pattern around the home position
            patrolPoints = new Transform[4];
            for (int i = 0; i < 4; i++)
            {
                GameObject patrolPoint = new GameObject($"PatrolPoint_{i}");
                patrolPoint.transform.SetParent(transform);

                float angle = i * 90f * Mathf.Deg2Rad;
                Vector3 offset = new Vector3(Mathf.Cos(angle), 0, Mathf.Sin(angle)) * patrolRadius;
                patrolPoint.transform.position = homePosition + offset;

                patrolPoints[i] = patrolPoint.transform;
            }
        }

        private void MoveToNextPatrolPoint()
        {
            if (patrolPoints == null || patrolPoints.Length == 0) return;

            if (randomPatrol)
            {
                currentPatrolIndex = Random.Range(0, patrolPoints.Length);
            }
            else
            {
                currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.Length;
            }

            unit.MoveTo(patrolPoints[currentPatrolIndex].position);
            patrolWaitStartTime = 0;
        }

        private void StartRandomPatrol()
        {
            Vector3 randomDirection = Random.insideUnitSphere * patrolRadius;
            randomDirection.y = 0;
            Vector3 targetPosition = randomPatrolCenter + randomDirection;

            // Ensure the position is valid
            if (PathfindingManager.Instance != null)
            {
                targetPosition = PathfindingManager.Instance.GetNearestValidPosition(targetPosition);
            }

            unit.MoveTo(targetPosition);
            patrolWaitStartTime = 0;
        }

        private Vector3 CalculateRetreatPosition()
        {
            Vector3 retreatDirection = Vector3.zero;

            // Calculate direction away from all known enemies
            foreach (Unit enemy in unit.DetectedEnemies)
            {
                Vector3 directionFromEnemy = (transform.position - enemy.transform.position).normalized;
                retreatDirection += directionFromEnemy;
            }

            if (retreatDirection == Vector3.zero)
            {
                // No enemies detected, retreat towards home
                retreatDirection = (homePosition - transform.position).normalized;
            }
            else
            {
                retreatDirection = retreatDirection.normalized;
            }

            Vector3 retreatPosition = transform.position + retreatDirection * retreatDistance;

            // Ensure the position is valid
            if (PathfindingManager.Instance != null)
            {
                retreatPosition = PathfindingManager.Instance.GetNearestValidPosition(retreatPosition);
            }

            return retreatPosition;
        }

        private void SetPrimaryTarget(Unit target)
        {
            primaryTarget = target;
            if (target != null)
            {
                unit.SetTarget(target);
                lastKnownEnemyPosition = target.transform.position;
                lastEnemyContactTime = Time.time;
            }
            else
            {
                unit.SetTarget(null);
            }
        }

        private void CallForReinforcements()
        {
            hasCalledForReinforcements = true;

            // Find nearby allied AI units and alert them
            Collider[] nearbyColliders = Physics.OverlapSphere(transform.position, reinforcementCallRange);

            foreach (Collider col in nearbyColliders)
            {
                EnemyAI allyAI = col.GetComponent<EnemyAI>();
                if (allyAI != null && allyAI != this && allyAI.unit.Faction == unit.Faction)
                {
                    // Alert ally to come help
                    if (allyAI.currentState == AIState.Patrol || allyAI.currentState == AIState.Idle)
                    {
                        allyAI.RespondToReinforcement(transform.position, primaryTarget);
                    }
                }
            }
        }

        private void UpdateGroupCoordination()
        {
            // Update list of nearby allies
            nearbyAllies.Clear();
            Collider[] nearbyColliders = Physics.OverlapSphere(transform.position, groupCommunicationRange);

            foreach (Collider col in nearbyColliders)
            {
                EnemyAI allyAI = col.GetComponent<EnemyAI>();
                if (allyAI != null && allyAI != this && allyAI.unit.Faction == unit.Faction)
                {
                    nearbyAllies.Add(allyAI);
                }
            }
        }

        public void RespondToReinforcement(Vector3 alertPosition, Unit alertTarget)
        {
            if (currentState == AIState.Engage || currentState == AIState.Retreat) return;

            // Move towards the alert position
            lastKnownEnemyPosition = alertPosition;

            if (alertTarget != null && unit.DetectedEnemies.Contains(alertTarget))
            {
                SetPrimaryTarget(alertTarget);
                ChangeState(AIState.Engage);
            }
            else
            {
                ChangeState(AIState.Investigate);
            }
        }

        private Unit GetClosestEnemyInRange(float range)
        {
            Unit closestEnemy = null;
            float closestDistance = float.MaxValue;

            foreach (Unit enemy in unit.DetectedEnemies)
            {
                if (enemy == null) continue;

                float distance = Vector3.Distance(transform.position, enemy.transform.position);
                if (distance <= range && distance < closestDistance)
                {
                    closestDistance = distance;
                    closestEnemy = enemy;
                }
            }

            return closestEnemy;
        }

        #endregion

        #region Event Handlers

        private void OnEnemyDetected(Unit detector, Unit enemy)
        {
            if (!knownEnemies.Contains(enemy))
            {
                knownEnemies.Add(enemy);
            }

            lastEnemyContactTime = Time.time;
            lastKnownEnemyPosition = enemy.transform.position;

            // If we don't have a target and this enemy is close, engage
            if (primaryTarget == null && currentState != AIState.Retreat)
            {
                float distance = Vector3.Distance(transform.position, enemy.transform.position);
                if (distance <= engagementRange)
                {
                    SetPrimaryTarget(enemy);
                    ChangeState(AIState.Engage);
                }
            }
        }

        private void OnEnemyLost(Unit detector, Unit enemy)
        {
            knownEnemies.Remove(enemy);

            if (primaryTarget == enemy)
            {
                SetPrimaryTarget(null);
                ChangeState(AIState.Investigate);
            }
        }

        private void OnHealthChanged(Unit unit, int newHealth)
        {
            float healthPercentage = (float)newHealth / unit.MaxHealth;

            // Force retreat if health is critically low
            if (healthPercentage <= retreatHealthThreshold && currentState != AIState.Retreat)
            {
                ChangeState(AIState.Retreat);
            }
        }

        #endregion

        #region Public Interface

        /// <summary>
        /// Force the AI to investigate a specific position
        /// </summary>
        public void InvestigatePosition(Vector3 position)
        {
            lastKnownEnemyPosition = position;
            ChangeState(AIState.Investigate);
        }

        /// <summary>
        /// Force the AI to engage a specific target
        /// </summary>
        public void EngageTarget(Unit target)
        {
            if (target != null && FactionManager.Instance != null)
            {
                if (FactionManager.Instance.AreFactionsHostile(unit.Faction, target.Faction))
                {
                    SetPrimaryTarget(target);
                    ChangeState(AIState.Engage);
                }
            }
        }

        /// <summary>
        /// Set new patrol points for this AI
        /// </summary>
        public void SetPatrolPoints(Transform[] newPatrolPoints)
        {
            patrolPoints = newPatrolPoints;
            currentPatrolIndex = 0;
        }

        /// <summary>
        /// Get current AI status for debugging
        /// </summary>
        public string GetAIStatus()
        {
            return $"State: {currentState}, Target: {(primaryTarget != null ? primaryTarget.name : "None")}, " +
                   $"Enemies: {knownEnemies.Count}, Health: {unit.CurrentHealth}/{unit.MaxHealth}";
        }

        #endregion
    }

    /// <summary>
    /// AI State enumeration for enemy unit behavior
    /// </summary>
    public enum AIState
    {
        Idle,           // Standing still, no activity
        Patrol,         // Following patrol route or random movement
        Investigate,    // Moving to investigate last known enemy position
        Engage,         // Actively fighting enemies
        Retreat         // Withdrawing due to low health or overwhelming odds
    }
}
