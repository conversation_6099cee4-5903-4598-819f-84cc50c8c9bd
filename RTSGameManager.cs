using UnityEngine;
using System.Collections.Generic;

namespace ColdVor.RTS
{
    /// <summary>
    /// Central game manager that coordinates all RTS systems
    /// Ensures proper initialization order and system integration
    /// </summary>
    public class RTSGameManager : MonoBehaviour
    {
        [Header("System References")]
        [SerializeField] private FactionManager factionManager;
        [SerializeField] private CombatSystem combatSystem;
        [SerializeField] private VisionSystem visionSystem;
        [SerializeField] private IntelligenceNetwork intelligenceNetwork;
        [SerializeField] private CommandHierarchy commandHierarchy;
        [SerializeField] private TacticalAwareness tacticalAwareness;

        [Header("Game Settings")]
        [SerializeField] private bool autoInitializeSystems = true;
        [SerializeField] private bool enableDebugMode = false;
        [SerializeField] private float systemUpdateInterval = 1f;

        [Header("Unit Spawning")]
        [SerializeField] private GameObject[] playerUnitPrefabs;
        [SerializeField] private GameObject[] enemyUnitPrefabs;
        [SerializeField] private Transform[] playerSpawnPoints;
        [SerializeField] private Transform[] enemySpawnPoints;

        // System status tracking
        private Dictionary<string, bool> systemStatus = new Dictionary<string, bool>();
        private float lastSystemUpdate;
        private bool allSystemsInitialized = false;

        // Singleton pattern
        private static RTSGameManager instance;
        public static RTSGameManager Instance => instance;

        // Events
        public System.Action OnAllSystemsInitialized;
        public System.Action<string, bool> OnSystemStatusChanged;

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
                
                if (autoInitializeSystems)
                {
                    InitializeAllSystems();
                }
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            if (autoInitializeSystems)
            {
                StartCoroutine(WaitForSystemInitialization());
            }
        }

        private void Update()
        {
            if (allSystemsInitialized && Time.time - lastSystemUpdate >= systemUpdateInterval)
            {
                UpdateSystemStatus();
                lastSystemUpdate = Time.time;
            }

            if (enableDebugMode)
            {
                HandleDebugInput();
            }
        }

        #region System Initialization

        private void InitializeAllSystems()
        {
            Debug.Log("RTSGameManager: Initializing all systems...");

            // Initialize systems in proper order
            InitializeFactionManager();
            InitializeCombatSystem();
            InitializeVisionSystem();
            InitializeIntelligenceNetwork();
            InitializeCommandHierarchy();
            InitializeTacticalAwareness();

            // Subscribe to system events
            SubscribeToSystemEvents();
        }

        private void InitializeFactionManager()
        {
            if (factionManager == null)
            {
                GameObject fmGO = new GameObject("FactionManager");
                factionManager = fmGO.AddComponent<FactionManager>();
            }
            systemStatus["FactionManager"] = true;
            Debug.Log("FactionManager initialized");
        }

        private void InitializeCombatSystem()
        {
            if (combatSystem == null)
            {
                GameObject csGO = new GameObject("CombatSystem");
                combatSystem = csGO.AddComponent<CombatSystem>();
            }
            systemStatus["CombatSystem"] = true;
            Debug.Log("CombatSystem initialized");
        }

        private void InitializeVisionSystem()
        {
            if (visionSystem == null)
            {
                GameObject vsGO = new GameObject("VisionSystem");
                visionSystem = vsGO.AddComponent<VisionSystem>();
            }
            systemStatus["VisionSystem"] = true;
            Debug.Log("VisionSystem initialized");
        }

        private void InitializeIntelligenceNetwork()
        {
            if (intelligenceNetwork == null)
            {
                GameObject inGO = new GameObject("IntelligenceNetwork");
                intelligenceNetwork = inGO.AddComponent<IntelligenceNetwork>();
            }
            systemStatus["IntelligenceNetwork"] = true;
            Debug.Log("IntelligenceNetwork initialized");
        }

        private void InitializeCommandHierarchy()
        {
            if (commandHierarchy == null)
            {
                GameObject chGO = new GameObject("CommandHierarchy");
                commandHierarchy = chGO.AddComponent<CommandHierarchy>();
            }
            systemStatus["CommandHierarchy"] = true;
            Debug.Log("CommandHierarchy initialized");
        }

        private void InitializeTacticalAwareness()
        {
            if (tacticalAwareness == null)
            {
                GameObject taGO = new GameObject("TacticalAwareness");
                tacticalAwareness = taGO.AddComponent<TacticalAwareness>();
            }
            systemStatus["TacticalAwareness"] = true;
            Debug.Log("TacticalAwareness initialized");
        }

        private System.Collections.IEnumerator WaitForSystemInitialization()
        {
            // Wait a frame for all systems to initialize
            yield return new WaitForEndOfFrame();
            
            // Check if all systems are ready
            bool allReady = true;
            foreach (var status in systemStatus.Values)
            {
                if (!status)
                {
                    allReady = false;
                    break;
                }
            }

            if (allReady)
            {
                allSystemsInitialized = true;
                OnAllSystemsInitialized?.Invoke();
                Debug.Log("All RTS systems initialized successfully!");

                // Spawn initial units if configured
                if (playerSpawnPoints.Length > 0 && playerUnitPrefabs.Length > 0)
                {
                    SpawnInitialUnits();
                }
            }
            else
            {
                Debug.LogWarning("Some RTS systems failed to initialize properly");
            }
        }

        #endregion

        #region System Integration

        private void SubscribeToSystemEvents()
        {
            // Subscribe to key system events for coordination
            if (intelligenceNetwork != null)
            {
                intelligenceNetwork.OnIntelReportCreated += OnIntelReportCreated;
                intelligenceNetwork.OnThreatLevelChanged += OnThreatLevelChanged;
            }

            if (commandHierarchy != null)
            {
                commandHierarchy.OnTacticalOrderIssued += OnTacticalOrderIssued;
            }

            if (tacticalAwareness != null)
            {
                tacticalAwareness.OnTacticalDecisionMade += OnTacticalDecisionMade;
            }

            if (combatSystem != null)
            {
                combatSystem.OnUnitKilled += OnUnitKilled;
            }
        }

        private void OnIntelReportCreated(IntelReport report)
        {
            if (enableDebugMode)
            {
                Debug.Log($"Intel Report: {report.intelType} - {report.subjectFaction} unit spotted by {report.reporterFaction}");
            }
        }

        private void OnThreatLevelChanged(Faction faction, ThreatLevel newLevel)
        {
            if (enableDebugMode)
            {
                Debug.Log($"Threat Level Changed: {faction} now at {newLevel}");
            }

            // Could trigger UI updates or other responses here
        }

        private void OnTacticalOrderIssued(TacticalOrder order)
        {
            if (enableDebugMode)
            {
                Debug.Log($"Tactical Order: {order.commander.name} issued {order.orderType} to {order.assignedUnits.Count} units");
            }
        }

        private void OnTacticalDecisionMade(TacticalDecision decision)
        {
            if (enableDebugMode)
            {
                Debug.Log($"Tactical Decision: {decision.unit.name} decided to {decision.decisionType} (confidence: {decision.confidence:F2})");
            }
        }

        private void OnUnitKilled(Unit killedUnit)
        {
            if (enableDebugMode)
            {
                Debug.Log($"Unit Killed: {killedUnit.name} ({killedUnit.Faction})");
            }

            // Refresh systems that track units
            if (visionSystem != null)
            {
                visionSystem.RefreshUnitLists();
            }
        }

        #endregion

        #region Unit Management

        private void SpawnInitialUnits()
        {
            // Spawn player units
            for (int i = 0; i < playerSpawnPoints.Length && i < playerUnitPrefabs.Length; i++)
            {
                if (playerSpawnPoints[i] != null && playerUnitPrefabs[i] != null)
                {
                    GameObject playerUnit = Instantiate(playerUnitPrefabs[i], playerSpawnPoints[i].position, playerSpawnPoints[i].rotation);
                    Unit unit = playerUnit.GetComponent<Unit>();
                    if (unit != null)
                    {
                        // Ensure player units are set to player faction
                        SetUnitFaction(unit, Faction.Player);
                    }
                }
            }

            // Spawn enemy units
            for (int i = 0; i < enemySpawnPoints.Length && i < enemyUnitPrefabs.Length; i++)
            {
                if (enemySpawnPoints[i] != null && enemyUnitPrefabs[i] != null)
                {
                    GameObject enemyUnit = Instantiate(enemyUnitPrefabs[i], enemySpawnPoints[i].position, enemySpawnPoints[i].rotation);
                    Unit unit = enemyUnit.GetComponent<Unit>();
                    if (unit != null)
                    {
                        // Ensure enemy units are set to enemy faction and have AI
                        SetUnitFaction(unit, Faction.Enemy);
                        
                        // Add AI component if not present
                        if (unit.GetComponent<EnemyAI>() == null)
                        {
                            unit.gameObject.AddComponent<EnemyAI>();
                        }
                    }
                }
            }

            Debug.Log($"Spawned {playerSpawnPoints.Length} player units and {enemySpawnPoints.Length} enemy units");
        }

        private void SetUnitFaction(Unit unit, Faction faction)
        {
            // Use reflection to set the faction field since it's private
            var factionField = typeof(Unit).GetField("faction", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (factionField != null)
            {
                factionField.SetValue(unit, faction);
            }
        }

        #endregion

        #region System Monitoring

        private void UpdateSystemStatus()
        {
            // Check if all systems are still functioning
            CheckSystemHealth("FactionManager", FactionManager.Instance != null);
            CheckSystemHealth("CombatSystem", CombatSystem.Instance != null);
            CheckSystemHealth("VisionSystem", VisionSystem.Instance != null);
            CheckSystemHealth("IntelligenceNetwork", IntelligenceNetwork.Instance != null);
            CheckSystemHealth("CommandHierarchy", CommandHierarchy.Instance != null);
            CheckSystemHealth("TacticalAwareness", TacticalAwareness.Instance != null);
        }

        private void CheckSystemHealth(string systemName, bool isHealthy)
        {
            if (systemStatus.ContainsKey(systemName))
            {
                if (systemStatus[systemName] != isHealthy)
                {
                    systemStatus[systemName] = isHealthy;
                    OnSystemStatusChanged?.Invoke(systemName, isHealthy);
                    
                    if (!isHealthy)
                    {
                        Debug.LogError($"System {systemName} has failed!");
                    }
                    else
                    {
                        Debug.Log($"System {systemName} has recovered");
                    }
                }
            }
        }

        #endregion

        #region Debug Functions

        private void HandleDebugInput()
        {
            if (Input.GetKeyDown(KeyCode.F1))
            {
                DebugPrintSystemStatus();
            }

            if (Input.GetKeyDown(KeyCode.F2))
            {
                DebugSpawnEnemyUnit();
            }

            if (Input.GetKeyDown(KeyCode.F3))
            {
                DebugTriggerTacticalAssessment();
            }
        }

        private void DebugPrintSystemStatus()
        {
            Debug.Log("=== RTS System Status ===");
            foreach (var kvp in systemStatus)
            {
                Debug.Log($"{kvp.Key}: {(kvp.Value ? "OK" : "FAILED")}");
            }

            if (intelligenceNetwork != null)
            {
                var playerIntel = intelligenceNetwork.GetFactionIntelligence(Faction.Player);
                Debug.Log($"Player Intelligence Reports: {playerIntel.Count}");
            }
        }

        private void DebugSpawnEnemyUnit()
        {
            if (enemyUnitPrefabs.Length > 0)
            {
                Vector3 spawnPos = Camera.main.transform.position + Camera.main.transform.forward * 10f;
                GameObject enemyUnit = Instantiate(enemyUnitPrefabs[0], spawnPos, Quaternion.identity);
                Unit unit = enemyUnit.GetComponent<Unit>();
                if (unit != null)
                {
                    SetUnitFaction(unit, Faction.Enemy);
                    enemyUnit.AddComponent<EnemyAI>();
                }
                Debug.Log("Debug: Spawned enemy unit");
            }
        }

        private void DebugTriggerTacticalAssessment()
        {
            if (tacticalAwareness != null)
            {
                Unit[] allUnits = FindObjectsByType<Unit>(FindObjectsSortMode.None);
                foreach (Unit unit in allUnits)
                {
                    tacticalAwareness.ForceAssessment(unit);
                }
                Debug.Log("Debug: Triggered tactical assessment for all units");
            }
        }

        #endregion

        #region Public Interface

        /// <summary>
        /// Check if all systems are initialized and ready
        /// </summary>
        public bool AreAllSystemsReady()
        {
            return allSystemsInitialized;
        }

        /// <summary>
        /// Get the status of a specific system
        /// </summary>
        public bool GetSystemStatus(string systemName)
        {
            return systemStatus.ContainsKey(systemName) ? systemStatus[systemName] : false;
        }

        /// <summary>
        /// Manually refresh all systems (useful after spawning/destroying units)
        /// </summary>
        public void RefreshAllSystems()
        {
            if (visionSystem != null) visionSystem.RefreshUnitLists();
            // Add other system refresh calls as needed
        }

        #endregion
    }
}
