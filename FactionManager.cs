using System.Collections.Generic;
using UnityEngine;

namespace ColdVor.RTS
{
    /// <summary>
    /// Manages faction relationships, team colors, and faction-based logic
    /// Singleton pattern for global access to faction information
    /// </summary>
    public class FactionManager : MonoBehaviour
    {
        [Header("Faction Colors")]
        [SerializeField] private Color playerColor = Color.blue;
        [SerializeField] private Color enemyColor = Color.red;
        [SerializeField] private Color neutralColor = Color.gray;
        [SerializeField] private Color allyColor = Color.green;

        [Header("Faction Settings")]
        [SerializeField] private bool enableFriendlyFire = false;
        [SerializeField] private float defaultDetectionRange = 15f;
        [SerializeField] private float defaultCommunicationRange = 25f;

        // Faction relationship matrix
        private Dictionary<(Faction, Faction), FactionRelationship> factionRelationships;
        
        // Faction data
        private Dictionary<Faction, FactionData> factionData;

        // Singleton pattern
        private static FactionManager instance;
        public static FactionManager Instance => instance;

        // Events
        public System.Action<Faction, Faction, FactionRelationship> OnFactionRelationshipChanged;

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeFactions();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void InitializeFactions()
        {
            // Initialize faction relationships
            factionRelationships = new Dictionary<(Faction, Faction), FactionRelationship>();
            factionData = new Dictionary<Faction, FactionData>();

            // Set up default relationships
            SetFactionRelationship(Faction.Player, Faction.Player, FactionRelationship.Friendly);
            SetFactionRelationship(Faction.Player, Faction.Enemy, FactionRelationship.Hostile);
            SetFactionRelationship(Faction.Player, Faction.Neutral, FactionRelationship.Neutral);
            SetFactionRelationship(Faction.Player, Faction.Ally, FactionRelationship.Friendly);

            SetFactionRelationship(Faction.Enemy, Faction.Enemy, FactionRelationship.Friendly);
            SetFactionRelationship(Faction.Enemy, Faction.Neutral, FactionRelationship.Neutral);
            SetFactionRelationship(Faction.Enemy, Faction.Ally, FactionRelationship.Hostile);

            SetFactionRelationship(Faction.Neutral, Faction.Neutral, FactionRelationship.Neutral);
            SetFactionRelationship(Faction.Neutral, Faction.Ally, FactionRelationship.Neutral);

            SetFactionRelationship(Faction.Ally, Faction.Ally, FactionRelationship.Friendly);

            // Initialize faction data
            factionData[Faction.Player] = new FactionData
            {
                faction = Faction.Player,
                color = playerColor,
                detectionRange = defaultDetectionRange,
                communicationRange = defaultCommunicationRange,
                canBeSelectedByPlayer = true
            };

            factionData[Faction.Enemy] = new FactionData
            {
                faction = Faction.Enemy,
                color = enemyColor,
                detectionRange = defaultDetectionRange,
                communicationRange = defaultCommunicationRange,
                canBeSelectedByPlayer = false
            };

            factionData[Faction.Neutral] = new FactionData
            {
                faction = Faction.Neutral,
                color = neutralColor,
                detectionRange = defaultDetectionRange * 0.5f,
                communicationRange = defaultCommunicationRange * 0.5f,
                canBeSelectedByPlayer = false
            };

            factionData[Faction.Ally] = new FactionData
            {
                faction = Faction.Ally,
                color = allyColor,
                detectionRange = defaultDetectionRange,
                communicationRange = defaultCommunicationRange,
                canBeSelectedByPlayer = true
            };
        }

        /// <summary>
        /// Get the relationship between two factions
        /// </summary>
        public FactionRelationship GetFactionRelationship(Faction faction1, Faction faction2)
        {
            var key = (faction1, faction2);
            if (factionRelationships.TryGetValue(key, out FactionRelationship relationship))
            {
                return relationship;
            }

            // Try reverse lookup
            var reverseKey = (faction2, faction1);
            if (factionRelationships.TryGetValue(reverseKey, out relationship))
            {
                return relationship;
            }

            // Default to neutral if no relationship defined
            return FactionRelationship.Neutral;
        }

        /// <summary>
        /// Set the relationship between two factions
        /// </summary>
        public void SetFactionRelationship(Faction faction1, Faction faction2, FactionRelationship relationship)
        {
            var key = (faction1, faction2);
            factionRelationships[key] = relationship;

            // Set reverse relationship as well
            var reverseKey = (faction2, faction1);
            factionRelationships[reverseKey] = relationship;

            OnFactionRelationshipChanged?.Invoke(faction1, faction2, relationship);
        }

        /// <summary>
        /// Check if two factions are hostile to each other
        /// </summary>
        public bool AreFactionsHostile(Faction faction1, Faction faction2)
        {
            return GetFactionRelationship(faction1, faction2) == FactionRelationship.Hostile;
        }

        /// <summary>
        /// Check if two factions are friendly to each other
        /// </summary>
        public bool AreFactionsFriendly(Faction faction1, Faction faction2)
        {
            return GetFactionRelationship(faction1, faction2) == FactionRelationship.Friendly;
        }

        /// <summary>
        /// Check if friendly fire is enabled and if damage should be applied
        /// </summary>
        public bool ShouldApplyDamage(Faction attackerFaction, Faction targetFaction)
        {
            var relationship = GetFactionRelationship(attackerFaction, targetFaction);
            
            if (relationship == FactionRelationship.Hostile)
                return true;
                
            if (relationship == FactionRelationship.Friendly)
                return enableFriendlyFire;
                
            return false; // Neutral factions don't take damage by default
        }

        /// <summary>
        /// Get faction data for a specific faction
        /// </summary>
        public FactionData GetFactionData(Faction faction)
        {
            return factionData.TryGetValue(faction, out FactionData data) ? data : null;
        }

        /// <summary>
        /// Get the color associated with a faction
        /// </summary>
        public Color GetFactionColor(Faction faction)
        {
            var data = GetFactionData(faction);
            return data?.color ?? Color.white;
        }

        /// <summary>
        /// Check if a faction can be selected by the player
        /// </summary>
        public bool CanFactionBeSelectedByPlayer(Faction faction)
        {
            var data = GetFactionData(faction);
            return data?.canBeSelectedByPlayer ?? false;
        }

        /// <summary>
        /// Get all units belonging to a specific faction
        /// </summary>
        public List<Unit> GetUnitsOfFaction(Faction faction)
        {
            List<Unit> factionUnits = new List<Unit>();
            Unit[] allUnits = FindObjectsByType<Unit>(FindObjectsSortMode.None);
            
            foreach (Unit unit in allUnits)
            {
                if (unit.Faction == faction)
                {
                    factionUnits.Add(unit);
                }
            }
            
            return factionUnits;
        }

        /// <summary>
        /// Get all hostile units relative to a specific faction
        /// </summary>
        public List<Unit> GetHostileUnits(Faction relativeTo)
        {
            List<Unit> hostileUnits = new List<Unit>();
            Unit[] allUnits = FindObjectsByType<Unit>(FindObjectsSortMode.None);
            
            foreach (Unit unit in allUnits)
            {
                if (AreFactionsHostile(relativeTo, unit.Faction))
                {
                    hostileUnits.Add(unit);
                }
            }
            
            return hostileUnits;
        }

        /// <summary>
        /// Get all friendly units relative to a specific faction
        /// </summary>
        public List<Unit> GetFriendlyUnits(Faction relativeTo)
        {
            List<Unit> friendlyUnits = new List<Unit>();
            Unit[] allUnits = FindObjectsByType<Unit>(FindObjectsSortMode.None);
            
            foreach (Unit unit in allUnits)
            {
                if (AreFactionsFriendly(relativeTo, unit.Faction))
                {
                    friendlyUnits.Add(unit);
                }
            }
            
            return friendlyUnits;
        }
    }

    /// <summary>
    /// Data structure containing faction-specific information
    /// </summary>
    [System.Serializable]
    public class FactionData
    {
        public Faction faction;
        public Color color;
        public float detectionRange;
        public float communicationRange;
        public bool canBeSelectedByPlayer;
    }
}
